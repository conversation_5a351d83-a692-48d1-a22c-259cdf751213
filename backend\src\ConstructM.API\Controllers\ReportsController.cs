using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using ConstructM.Infrastructure.Data;
using ConstructM.Domain.Entities;
using System.Security.Claims;

namespace ConstructM.API.Controllers;

[ApiController]
[Route("api/sites/{siteId}/[controller]")]
[Authorize]
public class ReportsController : ControllerBase
{
    private readonly ConstructMDbContext _context;
    private readonly ILogger<ReportsController> _logger;

    public ReportsController(ConstructMDbContext context, ILogger<ReportsController> logger)
    {
        _context = context;
        _logger = logger;
    }

    [HttpGet("summary")]
    public async Task<IActionResult> GetSiteSummary(Guid siteId)
    {
        try
        {
            var firmId = GetCurrentUserFirmId();
            
            var site = await _context.Sites
                .Where(s => s.Id == siteId && s.FirmId == firmId)
                .Include(s => s.MaterialLogs)
                .Include(s => s.Photos)
                .Include(s => s.Estimates)
                .FirstOrDefaultAsync();

            if (site == null)
            {
                return NotFound(new { message = "Site not found" });
            }

            // Calculate material costs by category
            var materialCostsByCategory = await _context.MaterialLogs
                .Where(ml => ml.SiteId == siteId)
                .GroupBy(ml => ml.Category)
                .Select(g => new
                {
                    Category = g.Key.ToString(),
                    TotalCost = g.Sum(ml => ml.TotalCost),
                    TotalQuantity = g.Sum(ml => ml.Quantity),
                    ItemCount = g.Count()
                })
                .ToListAsync();

            // Calculate monthly spending
            var monthlySpending = await _context.MaterialLogs
                .Where(ml => ml.SiteId == siteId)
                .GroupBy(ml => new { ml.LogDate.Year, ml.LogDate.Month })
                .Select(g => new
                {
                    Year = g.Key.Year,
                    Month = g.Key.Month,
                    TotalCost = g.Sum(ml => ml.TotalCost),
                    ItemCount = g.Count()
                })
                .OrderBy(x => x.Year)
                .ThenBy(x => x.Month)
                .ToListAsync();

            // Recent activities
            var recentMaterialLogs = await _context.MaterialLogs
                .Where(ml => ml.SiteId == siteId)
                .Include(ml => ml.LoggedByUser)
                .OrderByDescending(ml => ml.CreatedAt)
                .Take(10)
                .Select(ml => new
                {
                    ml.Id,
                    ml.MaterialName,
                    ml.Category,
                    ml.TotalCost,
                    ml.LogDate,
                    LoggedBy = ml.LoggedByUser.FullName
                })
                .ToListAsync();

            var recentPhotos = await _context.Photos
                .Where(p => p.SiteId == siteId)
                .Include(p => p.UploadedByUser)
                .OrderByDescending(p => p.CreatedAt)
                .Take(5)
                .Select(p => new
                {
                    p.Id,
                    p.Title,
                    p.Category,
                    p.PhotoTakenAt,
                    p.ThumbnailPath,
                    UploadedBy = p.UploadedByUser.FullName
                })
                .ToListAsync();

            var summary = new
            {
                Site = new
                {
                    site.Id,
                    site.Name,
                    site.Status,
                    site.Type,
                    site.EstimatedBudget,
                    site.ActualCost,
                    BudgetVariance = site.BudgetVariance,
                    BudgetVariancePercentage = site.BudgetVariancePercentage,
                    site.StartDate,
                    site.ExpectedEndDate,
                    site.ActualEndDate
                },
                Statistics = new
                {
                    TotalMaterialCost = site.MaterialLogs.Sum(ml => ml.TotalCost),
                    MaterialLogCount = site.MaterialLogs.Count,
                    PhotoCount = site.Photos.Count,
                    EstimateCount = site.Estimates.Count,
                    DaysInProgress = site.StartDate.HasValue 
                        ? (DateTime.UtcNow.Date - site.StartDate.Value.Date).Days 
                        : 0,
                    CompletionPercentage = CalculateCompletionPercentage(site)
                },
                MaterialCostsByCategory = materialCostsByCategory,
                MonthlySpending = monthlySpending,
                RecentActivities = new
                {
                    MaterialLogs = recentMaterialLogs,
                    Photos = recentPhotos
                }
            };

            return Ok(summary);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating site summary for site {SiteId}", siteId);
            return StatusCode(500, new { message = "An error occurred while generating the site summary" });
        }
    }

    [HttpGet("material-trends")]
    public async Task<IActionResult> GetMaterialTrends(Guid siteId, [FromQuery] int months = 6)
    {
        try
        {
            var firmId = GetCurrentUserFirmId();
            
            // Verify site belongs to user's firm
            var siteExists = await _context.Sites
                .AnyAsync(s => s.Id == siteId && s.FirmId == firmId);
            
            if (!siteExists)
            {
                return NotFound(new { message = "Site not found" });
            }

            var startDate = DateTime.UtcNow.AddMonths(-months);

            var trends = await _context.MaterialLogs
                .Where(ml => ml.SiteId == siteId && ml.LogDate >= startDate)
                .GroupBy(ml => new { 
                    ml.Category,
                    Year = ml.LogDate.Year,
                    Month = ml.LogDate.Month
                })
                .Select(g => new
                {
                    Category = g.Key.Category.ToString(),
                    Year = g.Key.Year,
                    Month = g.Key.Month,
                    TotalCost = g.Sum(ml => ml.TotalCost),
                    TotalQuantity = g.Sum(ml => ml.Quantity),
                    ItemCount = g.Count()
                })
                .OrderBy(x => x.Year)
                .ThenBy(x => x.Month)
                .ThenBy(x => x.Category)
                .ToListAsync();

            return Ok(trends);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating material trends for site {SiteId}", siteId);
            return StatusCode(500, new { message = "An error occurred while generating material trends" });
        }
    }

    [HttpGet("cost-analysis")]
    public async Task<IActionResult> GetCostAnalysis(Guid siteId)
    {
        try
        {
            var firmId = GetCurrentUserFirmId();
            
            var site = await _context.Sites
                .Where(s => s.Id == siteId && s.FirmId == firmId)
                .Include(s => s.MaterialLogs)
                .Include(s => s.Estimates)
                .FirstOrDefaultAsync();

            if (site == null)
            {
                return NotFound(new { message = "Site not found" });
            }

            var latestEstimate = site.Estimates
                .OrderByDescending(e => e.CreatedAt)
                .FirstOrDefault();

            var actualMaterialCost = site.MaterialLogs.Sum(ml => ml.TotalCost);
            var estimatedMaterialCost = latestEstimate?.MaterialCost ?? 0;

            var analysis = new
            {
                Budget = new
                {
                    Estimated = site.EstimatedBudget ?? 0,
                    Actual = site.ActualCost ?? 0,
                    Variance = site.BudgetVariance,
                    VariancePercentage = site.BudgetVariancePercentage
                },
                Materials = new
                {
                    Estimated = estimatedMaterialCost,
                    Actual = actualMaterialCost,
                    Variance = actualMaterialCost - estimatedMaterialCost,
                    VariancePercentage = estimatedMaterialCost > 0 
                        ? ((actualMaterialCost - estimatedMaterialCost) / estimatedMaterialCost) * 100 
                        : 0
                },
                Breakdown = new
                {
                    MaterialCost = actualMaterialCost,
                    LaborCost = latestEstimate?.LaborCost ?? 0,
                    EquipmentCost = latestEstimate?.EquipmentCost ?? 0,
                    OverheadCost = latestEstimate?.OverheadCost ?? 0,
                    ProfitMargin = latestEstimate?.ProfitMargin ?? 0
                }
            };

            return Ok(analysis);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating cost analysis for site {SiteId}", siteId);
            return StatusCode(500, new { message = "An error occurred while generating cost analysis" });
        }
    }

    private static decimal CalculateCompletionPercentage(Site site)
    {
        if (site.Status == SiteStatus.Completed)
            return 100;

        if (site.Status == SiteStatus.Planning || site.Status == SiteStatus.Cancelled)
            return 0;

        if (!site.StartDate.HasValue || !site.ExpectedEndDate.HasValue)
            return 0;

        var totalDays = (site.ExpectedEndDate.Value - site.StartDate.Value).TotalDays;
        var elapsedDays = (DateTime.UtcNow - site.StartDate.Value).TotalDays;

        if (totalDays <= 0)
            return 0;

        var percentage = (decimal)(elapsedDays / totalDays * 100);
        return Math.Max(0, Math.Min(100, percentage));
    }

    private Guid GetCurrentUserFirmId()
    {
        var firmIdClaim = User.FindFirst("FirmId")?.Value;
        return Guid.Parse(firmIdClaim ?? throw new UnauthorizedAccessException("Firm ID not found in token"));
    }
}
