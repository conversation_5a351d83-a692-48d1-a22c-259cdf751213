import React from 'react';
import { Package, DollarSign, TrendingUp, Activity } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/Card';

interface MaterialStats {
  totalCost: number;
  totalItems: number;
  costByCategory: Array<{ category: string; cost: number; count: number }>;
  recentLogs: any[];
}

interface MaterialStatsCardsProps {
  stats: MaterialStats;
}

export const MaterialStatsCards: React.FC<MaterialStatsCardsProps> = ({ stats }) => {
  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const topCategory = stats.costByCategory.length > 0 
    ? stats.costByCategory.reduce((prev, current) => 
        prev.cost > current.cost ? prev : current
      )
    : null;

  const averageCostPerItem = stats.totalItems > 0 
    ? stats.totalCost / stats.totalItems 
    : 0;

  return (
    <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                <DollarSign className="w-5 h-5 text-white" />
              </div>
            </div>
            <div className="ml-5 w-0 flex-1">
              <dl>
                <dt className="text-sm font-medium text-gray-500 truncate">
                  Total Cost
                </dt>
                <dd className="text-lg font-medium text-gray-900">
                  {formatCurrency(stats.totalCost)}
                </dd>
              </dl>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent className="p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                <Package className="w-5 h-5 text-white" />
              </div>
            </div>
            <div className="ml-5 w-0 flex-1">
              <dl>
                <dt className="text-sm font-medium text-gray-500 truncate">
                  Total Items
                </dt>
                <dd className="text-lg font-medium text-gray-900">
                  {stats.totalItems.toLocaleString()}
                </dd>
              </dl>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent className="p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                <TrendingUp className="w-5 h-5 text-white" />
              </div>
            </div>
            <div className="ml-5 w-0 flex-1">
              <dl>
                <dt className="text-sm font-medium text-gray-500 truncate">
                  Top Category
                </dt>
                <dd className="text-lg font-medium text-gray-900">
                  {topCategory ? topCategory.category : 'N/A'}
                </dd>
                {topCategory && (
                  <dd className="text-sm text-gray-500">
                    {formatCurrency(topCategory.cost)}
                  </dd>
                )}
              </dl>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent className="p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
                <Activity className="w-5 h-5 text-white" />
              </div>
            </div>
            <div className="ml-5 w-0 flex-1">
              <dl>
                <dt className="text-sm font-medium text-gray-500 truncate">
                  Avg. Cost/Item
                </dt>
                <dd className="text-lg font-medium text-gray-900">
                  {formatCurrency(averageCostPerItem)}
                </dd>
              </dl>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
