using System.ComponentModel.DataAnnotations;
using ConstructM.Domain.Common;

namespace ConstructM.Domain.Entities;

public class Firm : BaseEntity
{
    [Required]
    [MaxLength(200)]
    public string Name { get; set; } = string.Empty;
    
    [MaxLength(500)]
    public string? Description { get; set; }
    
    [Required]
    [EmailAddress]
    [MaxLength(255)]
    public string ContactEmail { get; set; } = string.Empty;
    
    [MaxLength(20)]
    public string? ContactPhone { get; set; }
    
    [MaxLength(500)]
    public string? Address { get; set; }
    
    [MaxLength(100)]
    public string? City { get; set; }
    
    [MaxLength(100)]
    public string? State { get; set; }
    
    [MaxLength(20)]
    public string? PostalCode { get; set; }
    
    [MaxLength(100)]
    public string? Country { get; set; }
    
    [MaxLength(50)]
    public string? RegistrationNumber { get; set; }
    
    [MaxLength(50)]
    public string? TaxId { get; set; }
    
    public bool IsActive { get; set; } = true;
    
    public DateTime? SubscriptionExpiresAt { get; set; }
    
    // Navigation properties
    public virtual ICollection<User> Users { get; set; } = new List<User>();
    public virtual ICollection<Site> Sites { get; set; } = new List<Site>();
}
