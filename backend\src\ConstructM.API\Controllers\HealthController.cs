using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using ConstructM.Infrastructure.Data;

namespace ConstructM.API.Controllers;

[ApiController]
[Route("api/[controller]")]
public class HealthController : ControllerBase
{
    private readonly ConstructMDbContext _context;
    private readonly ILogger<HealthController> _logger;

    public HealthController(ConstructMDbContext context, ILogger<HealthController> logger)
    {
        _context = context;
        _logger = logger;
    }

    [HttpGet]
    public async Task<IActionResult> Get()
    {
        try
        {
            // Test database connection
            var canConnect = await _context.Database.CanConnectAsync();
            
            var health = new
            {
                status = "healthy",
                timestamp = DateTime.UtcNow,
                version = "1.0.0",
                database = canConnect ? "connected" : "disconnected",
                environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Unknown"
            };

            return Ok(health);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Health check failed");
            
            var health = new
            {
                status = "unhealthy",
                timestamp = DateTime.UtcNow,
                version = "1.0.0",
                database = "error",
                environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Unknown",
                error = ex.Message
            };

            return StatusCode(500, health);
        }
    }

    [HttpGet("info")]
    public IActionResult GetInfo()
    {
        var info = new
        {
            application = "ConstructM API",
            version = "1.0.0",
            description = "Construction Site Management System API",
            environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Unknown",
            timestamp = DateTime.UtcNow,
            endpoints = new
            {
                auth = new[] { "POST /api/auth/register", "POST /api/auth/login" },
                sites = new[] { "GET /api/sites", "POST /api/sites", "GET /api/sites/{id}", "PUT /api/sites/{id}", "DELETE /api/sites/{id}" },
                materials = new[] { "GET /api/sites/{siteId}/materials", "POST /api/sites/{siteId}/materials" },
                photos = new[] { "GET /api/sites/{siteId}/photos", "POST /api/sites/{siteId}/photos" },
                reports = new[] { "GET /api/sites/{siteId}/reports/summary", "GET /api/sites/{siteId}/reports/material-trends" }
            }
        };

        return Ok(info);
    }
}
