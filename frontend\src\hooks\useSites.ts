import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { sitesService } from '@/services/sites';
import type { Site, CreateSiteRequest, UpdateSiteRequest } from '@/types';

// Query keys
export const siteKeys = {
  all: ['sites'] as const,
  lists: () => [...siteKeys.all, 'list'] as const,
  list: (filters: string) => [...siteKeys.lists(), { filters }] as const,
  details: () => [...siteKeys.all, 'detail'] as const,
  detail: (id: string) => [...siteKeys.details(), id] as const,
  statistics: () => [...siteKeys.all, 'statistics'] as const,
};

// Get all sites
export const useSites = () => {
  return useQuery({
    queryKey: siteKeys.lists(),
    queryFn: sitesService.getAll,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Get site by ID
export const useSite = (id: string) => {
  return useQuery({
    queryKey: siteKeys.detail(id),
    queryFn: () => sitesService.getById(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000,
  });
};

// Get site statistics
export const useSiteStatistics = () => {
  return useQuery({
    queryKey: siteKeys.statistics(),
    queryFn: sitesService.getStatistics,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
};

// Get recent sites
export const useRecentSites = (limit: number = 5) => {
  return useQuery({
    queryKey: [...siteKeys.lists(), 'recent', limit],
    queryFn: () => sitesService.getRecent(limit),
    staleTime: 5 * 60 * 1000,
  });
};

// Create site mutation
export const useCreateSite = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateSiteRequest) => sitesService.create(data),
    onSuccess: () => {
      // Invalidate and refetch sites
      queryClient.invalidateQueries({ queryKey: siteKeys.all });
    },
  });
};

// Update site mutation
export const useUpdateSite = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateSiteRequest }) =>
      sitesService.update(id, data),
    onSuccess: (_, { id }) => {
      // Invalidate specific site and all sites list
      queryClient.invalidateQueries({ queryKey: siteKeys.detail(id) });
      queryClient.invalidateQueries({ queryKey: siteKeys.lists() });
      queryClient.invalidateQueries({ queryKey: siteKeys.statistics() });
    },
  });
};

// Delete site mutation
export const useDeleteSite = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => sitesService.delete(id),
    onSuccess: (_, id) => {
      // Remove from cache and invalidate lists
      queryClient.removeQueries({ queryKey: siteKeys.detail(id) });
      queryClient.invalidateQueries({ queryKey: siteKeys.lists() });
      queryClient.invalidateQueries({ queryKey: siteKeys.statistics() });
    },
  });
};

// Search sites
export const useSearchSites = (query: string) => {
  return useQuery({
    queryKey: [...siteKeys.lists(), 'search', query],
    queryFn: () => sitesService.search(query),
    enabled: query.length > 0,
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
};

// Filter sites by status
export const useSitesByStatus = (status: number) => {
  return useQuery({
    queryKey: [...siteKeys.lists(), 'status', status],
    queryFn: () => sitesService.filterByStatus(status),
    staleTime: 5 * 60 * 1000,
  });
};

// Filter sites by type
export const useSitesByType = (type: number) => {
  return useQuery({
    queryKey: [...siteKeys.lists(), 'type', type],
    queryFn: () => sitesService.filterByType(type),
    staleTime: 5 * 60 * 1000,
  });
};

// Upload site drawing
export const useUploadSiteDrawing = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      siteId,
      file,
      onProgress,
    }: {
      siteId: string;
      file: File;
      onProgress?: (progress: number) => void;
    }) => sitesService.uploadDrawing(siteId, file, onProgress),
    onSuccess: (_, { siteId }) => {
      // Invalidate specific site
      queryClient.invalidateQueries({ queryKey: siteKeys.detail(siteId) });
    },
  });
};
