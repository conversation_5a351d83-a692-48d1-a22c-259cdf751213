import React from 'react';
import { Link } from 'react-router-dom';
import { Plus, TrendingUp, AlertTriangle, CheckCircle } from 'lucide-react';
import { useSiteStatistics, useRecentSites } from '@/hooks/useSites';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { Button } from '@/components/ui/Button';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';
import { SiteType, SiteStatus } from '@/types';
// import { DashboardCharts } from '@/components/dashboard/DashboardCharts';
// import { RecentActivity } from '@/components/dashboard/RecentActivity';
// import { ProjectOverview } from '@/components/dashboard/ProjectOverview';

export const DashboardPage: React.FC = () => {
  const { data: statistics, isLoading: statsLoading } = useSiteStatistics();
  const { data: recentSites, isLoading: sitesLoading } = useRecentSites();

  const isLoading = statsLoading || sitesLoading;

  const getSiteTypeLabel = (type: SiteType): string => {
    const labels = {
      [SiteType.Residential]: 'Residential',
      [SiteType.Commercial]: 'Commercial',
      [SiteType.Industrial]: 'Industrial',
      [SiteType.Infrastructure]: 'Infrastructure',
      [SiteType.Renovation]: 'Renovation',
      [SiteType.Other]: 'Other',
    };
    return labels[type] || 'Unknown';
  };

  const getSiteStatusLabel = (status: SiteStatus): string => {
    const labels = {
      [SiteStatus.Planning]: 'Planning',
      [SiteStatus.InProgress]: 'In Progress',
      [SiteStatus.OnHold]: 'On Hold',
      [SiteStatus.Completed]: 'Completed',
      [SiteStatus.Cancelled]: 'Cancelled',
    };
    return labels[status] || 'Unknown';
  };

  const getStatusColor = (status: SiteStatus): string => {
    const colors = {
      [SiteStatus.Planning]: 'bg-yellow-100 text-yellow-800',
      [SiteStatus.InProgress]: 'bg-blue-100 text-blue-800',
      [SiteStatus.OnHold]: 'bg-gray-100 text-gray-800',
      [SiteStatus.Completed]: 'bg-green-100 text-green-800',
      [SiteStatus.Cancelled]: 'bg-red-100 text-red-800',
    };
    return colors[status] || 'bg-gray-100 text-gray-800';
  };

  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  // Mock data for enhanced dashboard
  const mockChartData = {
    monthlySpending: [
      { month: 'Jan', amount: 45000 },
      { month: 'Feb', amount: 52000 },
      { month: 'Mar', amount: 48000 },
      { month: 'Apr', amount: 61000 },
      { month: 'May', amount: 55000 },
      { month: 'Jun', amount: 67000 },
    ],
    projectProgress: [
      { name: 'Planning', value: 2, color: '#FCD34D' },
      { name: 'In Progress', value: 8, color: '#60A5FA' },
      { name: 'On Hold', value: 1, color: '#9CA3AF' },
      { name: 'Completed', value: 12, color: '#34D399' },
    ],
    costByCategory: [
      { category: 'Materials', amount: 125000, percentage: 45 },
      { category: 'Labor', amount: 89000, percentage: 32 },
      { category: 'Equipment', amount: 34000, percentage: 12 },
      { category: 'Other', amount: 30000, percentage: 11 },
    ],
  };

  const mockActivity = [
    {
      id: '1',
      type: 'material_added',
      message: 'Added 500 bags of cement to Downtown Office',
      user: 'John Doe',
      timestamp: '2024-01-15T10:30:00Z',
      icon: 'package',
    },
    {
      id: '2',
      type: 'photo_uploaded',
      message: 'Uploaded 12 progress photos to Residential Complex',
      user: 'Jane Smith',
      timestamp: '2024-01-15T09:15:00Z',
      icon: 'camera',
    },
    {
      id: '3',
      type: 'site_updated',
      message: 'Updated status of Shopping Mall to In Progress',
      user: 'Mike Johnson',
      timestamp: '2024-01-15T08:45:00Z',
      icon: 'edit',
    },
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="md:flex md:items-center md:justify-between">
        <div className="flex-1 min-w-0">
          <h2 className="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
            Dashboard
          </h2>
          <p className="mt-1 text-sm text-gray-500">
            Welcome back! Here's an overview of your construction projects.
          </p>
        </div>
        <div className="mt-4 flex md:mt-0 md:ml-4 space-x-3">
          <Button
            variant="outline"
            icon={<TrendingUp className="w-4 h-4" />}
          >
            View Reports
          </Button>
          <Button
            icon={<Plus className="w-4 h-4" />}
            onClick={() => window.location.href = '/sites/new'}
          >
            New Site
          </Button>
        </div>
      </div>

      {/* Enhanced Statistics Cards */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        <Card hover>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center">
                  <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M10.707 2.293a1 1 0 00-1.414 0l-9 9a1 1 0 001.414 1.414L10 4.414l8.293 8.293a1 1 0 001.414-1.414l-9-9z" />
                  </svg>
                </div>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    Total Sites
                  </dt>
                  <dd className="text-2xl font-bold text-gray-900">
                    {statistics?.totalSites || 23}
                  </dd>
                  <dd className="text-sm text-green-600 flex items-center">
                    <TrendingUp className="w-3 h-3 mr-1" />
                    +2 this month
                  </dd>
                </dl>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card hover>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center">
                  <CheckCircle className="w-6 h-6 text-white" />
                </div>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    Active Sites
                  </dt>
                  <dd className="text-2xl font-bold text-gray-900">
                    {statistics?.activeSites || 8}
                  </dd>
                  <dd className="text-sm text-blue-600">
                    65% completion avg.
                  </dd>
                </dl>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card hover>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-10 h-10 bg-yellow-500 rounded-lg flex items-center justify-center">
                  <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z" />
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z" clipRule="evenodd" />
                  </svg>
                </div>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    Total Budget
                  </dt>
                  <dd className="text-2xl font-bold text-gray-900">
                    {formatCurrency(statistics?.totalBudget || 2850000)}
                  </dd>
                  <dd className="text-sm text-gray-600">
                    Across all projects
                  </dd>
                </dl>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card hover>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-10 h-10 bg-red-500 rounded-lg flex items-center justify-center">
                  <AlertTriangle className="w-6 h-6 text-white" />
                </div>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    Total Spent
                  </dt>
                  <dd className="text-2xl font-bold text-gray-900">
                    {formatCurrency(statistics?.totalSpent || 1890000)}
                  </dd>
                  <dd className="text-sm text-red-600">
                    66% of budget used
                  </dd>
                </dl>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Charts and Analytics - Coming Soon */}
      <Card>
        <CardContent className="p-12 text-center">
          <TrendingUp className="mx-auto h-12 w-12 text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Advanced Analytics</h3>
          <p className="text-gray-500">Interactive charts and detailed analytics coming soon.</p>
        </CardContent>
      </Card>

      {/* Project Overview and Recent Activity - Coming Soon */}
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Project Overview</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-500 text-center py-8">Project overview component coming soon.</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Recent Activity</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-500 text-center py-8">Activity feed coming soon.</p>
          </CardContent>
        </Card>
      </div>

      {/* Recent Sites */}
      <div className="card">
        <div className="card-header">
          <div className="flex items-center justify-between">
            <h3 className="text-lg leading-6 font-medium text-gray-900">
              Recent Sites
            </h3>
            <Link
              to="/sites"
              className="text-sm font-medium text-primary-600 hover:text-primary-500"
            >
              View all
            </Link>
          </div>
        </div>
        <div className="card-content">
          {recentSites && recentSites.length > 0 ? (
            <div className="flow-root">
              <ul className="-my-5 divide-y divide-gray-200">
                {recentSites.map((site) => (
                  <li key={site.id} className="py-5">
                    <div className="flex items-center space-x-4">
                      <div className="flex-shrink-0">
                        <div className="h-10 w-10 bg-primary-100 rounded-lg flex items-center justify-center">
                          <span className="text-primary-600 font-medium text-sm">
                            {site.name.charAt(0)}
                          </span>
                        </div>
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-900 truncate">
                          {site.name}
                        </p>
                        <p className="text-sm text-gray-500 truncate">
                          {site.location} • {getSiteTypeLabel(site.type)}
                        </p>
                      </div>
                      <div className="flex-shrink-0">
                        <span
                          className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(
                            site.status
                          )}`}
                        >
                          {getSiteStatusLabel(site.status)}
                        </span>
                      </div>
                      <div className="flex-shrink-0">
                        <Link
                          to={`/sites/${site.id}`}
                          className="text-primary-600 hover:text-primary-500"
                        >
                          <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path
                              fillRule="evenodd"
                              d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                              clipRule="evenodd"
                            />
                          </svg>
                        </Link>
                      </div>
                    </div>
                  </li>
                ))}
              </ul>
            </div>
          ) : (
            <div className="text-center py-6">
              <svg
                className="mx-auto h-12 w-12 text-gray-400"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
                />
              </svg>
              <h3 className="mt-2 text-sm font-medium text-gray-900">No sites</h3>
              <p className="mt-1 text-sm text-gray-500">
                Get started by creating your first construction site.
              </p>
              <div className="mt-6">
                <Link
                  to="/sites/new"
                  className="btn-primary"
                >
                  New Site
                </Link>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
