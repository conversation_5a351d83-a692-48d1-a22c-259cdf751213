using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using ConstructM.Domain.Common;

namespace ConstructM.Domain.Entities;

public class Site : BaseEntity
{
    [Required]
    [MaxLength(200)]
    public string Name { get; set; } = string.Empty;
    
    [MaxLength(1000)]
    public string? Description { get; set; }
    
    [Required]
    [MaxLength(500)]
    public string Location { get; set; } = string.Empty;
    
    [MaxLength(500)]
    public string? Address { get; set; }
    
    [MaxLength(100)]
    public string? City { get; set; }
    
    [MaxLength(100)]
    public string? State { get; set; }
    
    [MaxLength(20)]
    public string? PostalCode { get; set; }
    
    [Column(TypeName = "decimal(18,2)")]
    public decimal? Area { get; set; }
    
    [MaxLength(50)]
    public string? AreaUnit { get; set; } = "sqft";
    
    [Required]
    public SiteType Type { get; set; } = SiteType.Residential;
    
    [Required]
    public SiteStatus Status { get; set; } = SiteStatus.Planning;
    
    public DateTime? StartDate { get; set; }
    
    public DateTime? ExpectedEndDate { get; set; }
    
    public DateTime? ActualEndDate { get; set; }
    
    [Column(TypeName = "decimal(18,2)")]
    public decimal? EstimatedBudget { get; set; }
    
    [Column(TypeName = "decimal(18,2)")]
    public decimal? ActualCost { get; set; }
    
    [MaxLength(500)]
    public string? DrawingUrl { get; set; }
    
    [MaxLength(100)]
    public string? DrawingFileName { get; set; }
    
    public long? DrawingFileSize { get; set; }
    
    [MaxLength(1000)]
    public string? Notes { get; set; }
    
    // Navigation properties
    public Guid FirmId { get; set; }
    public virtual Firm Firm { get; set; } = null!;
    
    public Guid CreatedByUserId { get; set; }
    public virtual User CreatedByUser { get; set; } = null!;
    
    public virtual ICollection<Estimate> Estimates { get; set; } = new List<Estimate>();
    public virtual ICollection<MaterialLog> MaterialLogs { get; set; } = new List<MaterialLog>();
    public virtual ICollection<Photo> Photos { get; set; } = new List<Photo>();
    
    // Computed properties
    public decimal BudgetVariance => ActualCost.HasValue && EstimatedBudget.HasValue 
        ? ActualCost.Value - EstimatedBudget.Value 
        : 0;
        
    public decimal BudgetVariancePercentage => EstimatedBudget.HasValue && EstimatedBudget.Value > 0 && ActualCost.HasValue
        ? (BudgetVariance / EstimatedBudget.Value) * 100
        : 0;
}

public enum SiteType
{
    Residential = 0,
    Commercial = 1,
    Industrial = 2,
    Infrastructure = 3,
    Renovation = 4,
    Other = 5
}

public enum SiteStatus
{
    Planning = 0,
    InProgress = 1,
    OnHold = 2,
    Completed = 3,
    Cancelled = 4
}
