import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { X } from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { MaterialCategory, MaterialLogType } from '@/types';
import type { CreateMaterialLogRequest } from '@/types';

const materialSchema = z.object({
  materialName: z.string().min(1, 'Material name is required'),
  description: z.string().optional(),
  category: z.number(),
  quantity: z.number().positive('Quantity must be positive'),
  unit: z.string().min(1, 'Unit is required'),
  unitRate: z.number().positive('Unit rate must be positive'),
  logDate: z.string().optional(),
  logType: z.number(),
  supplier: z.string().optional(),
  invoiceNumber: z.string().optional(),
  notes: z.string().optional(),
});

type MaterialFormData = z.infer<typeof materialSchema>;

interface AddMaterialModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: CreateMaterialLogRequest) => Promise<void>;
  isLoading: boolean;
}

export const AddMaterialModal: React.FC<AddMaterialModalProps> = ({
  isOpen,
  onClose,
  onSubmit,
  isLoading,
}) => {
  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<MaterialFormData>({
    resolver: zodResolver(materialSchema),
    defaultValues: {
      category: MaterialCategory.Other,
      logType: MaterialLogType.Purchase,
      unit: 'pcs',
    },
  });

  const handleFormSubmit = async (data: MaterialFormData) => {
    const submitData: CreateMaterialLogRequest = {
      ...data,
      logDate: data.logDate || undefined,
    };
    
    await onSubmit(submitData);
    reset();
  };

  const getCategoryLabel = (category: number): string => {
    const labels = {
      [MaterialCategory.Concrete]: 'Concrete',
      [MaterialCategory.Steel]: 'Steel',
      [MaterialCategory.Wood]: 'Wood',
      [MaterialCategory.Brick]: 'Brick',
      [MaterialCategory.Tile]: 'Tile',
      [MaterialCategory.Paint]: 'Paint',
      [MaterialCategory.Electrical]: 'Electrical',
      [MaterialCategory.Plumbing]: 'Plumbing',
      [MaterialCategory.Roofing]: 'Roofing',
      [MaterialCategory.Insulation]: 'Insulation',
      [MaterialCategory.Hardware]: 'Hardware',
      [MaterialCategory.Tools]: 'Tools',
      [MaterialCategory.Equipment]: 'Equipment',
      [MaterialCategory.Other]: 'Other',
    };
    return labels[category] || 'Unknown';
  };

  const getTypeLabel = (type: number): string => {
    const labels = {
      [MaterialLogType.Purchase]: 'Purchase',
      [MaterialLogType.Usage]: 'Usage',
      [MaterialLogType.Return]: 'Return',
      [MaterialLogType.Waste]: 'Waste',
      [MaterialLogType.Transfer]: 'Transfer',
    };
    return labels[type] || 'Unknown';
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={onClose} />

        <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
          <form onSubmit={handleSubmit(handleFormSubmit)}>
            <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-medium text-gray-900">Add Material</h3>
                <button
                  type="button"
                  onClick={onClose}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <X className="w-6 h-6" />
                </button>
              </div>

              <div className="space-y-4">
                <Input
                  label="Material Name *"
                  {...register('materialName')}
                  error={errors.materialName?.message}
                  placeholder="Enter material name"
                />

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Description
                  </label>
                  <textarea
                    {...register('description')}
                    rows={3}
                    className="block w-full rounded-lg border border-gray-300 bg-white px-3 py-2 text-sm placeholder-gray-500 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                    placeholder="Enter description"
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Category *
                    </label>
                    <select
                      {...register('category', { valueAsNumber: true })}
                      className="block w-full rounded-lg border border-gray-300 bg-white px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                    >
                      {Object.entries(MaterialCategory).map(([key, value]) => (
                        <option key={key} value={value}>
                          {getCategoryLabel(value)}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Type *
                    </label>
                    <select
                      {...register('logType', { valueAsNumber: true })}
                      className="block w-full rounded-lg border border-gray-300 bg-white px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                    >
                      {Object.entries(MaterialLogType).map(([key, value]) => (
                        <option key={key} value={value}>
                          {getTypeLabel(value)}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>

                <div className="grid grid-cols-3 gap-4">
                  <Input
                    label="Quantity *"
                    type="number"
                    step="0.01"
                    {...register('quantity', { valueAsNumber: true })}
                    error={errors.quantity?.message}
                    placeholder="0.00"
                  />

                  <Input
                    label="Unit *"
                    {...register('unit')}
                    error={errors.unit?.message}
                    placeholder="pcs, kg, m³"
                  />

                  <Input
                    label="Unit Rate *"
                    type="number"
                    step="0.01"
                    {...register('unitRate', { valueAsNumber: true })}
                    error={errors.unitRate?.message}
                    placeholder="0.00"
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <Input
                    label="Date"
                    type="date"
                    {...register('logDate')}
                  />

                  <Input
                    label="Supplier"
                    {...register('supplier')}
                    placeholder="Enter supplier name"
                  />
                </div>

                <Input
                  label="Invoice Number"
                  {...register('invoiceNumber')}
                  placeholder="Enter invoice number"
                />

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Notes
                  </label>
                  <textarea
                    {...register('notes')}
                    rows={3}
                    className="block w-full rounded-lg border border-gray-300 bg-white px-3 py-2 text-sm placeholder-gray-500 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                    placeholder="Enter any additional notes"
                  />
                </div>
              </div>
            </div>

            <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
              <Button
                type="submit"
                loading={isLoading}
                className="w-full sm:w-auto sm:ml-3"
              >
                Add Material
              </Button>
              <Button
                type="button"
                variant="outline"
                onClick={onClose}
                className="mt-3 w-full sm:mt-0 sm:w-auto"
              >
                Cancel
              </Button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};
