using System.ComponentModel.DataAnnotations;
using ConstructM.Domain.Common;

namespace ConstructM.Domain.Entities;

public class User : BaseEntity
{
    [Required]
    [MaxLength(100)]
    public string FirstName { get; set; } = string.Empty;
    
    [Required]
    [MaxLength(100)]
    public string LastName { get; set; } = string.Empty;
    
    [Required]
    [EmailAddress]
    [MaxLength(255)]
    public string Email { get; set; } = string.Empty;
    
    [Required]
    public string PasswordHash { get; set; } = string.Empty;
    
    [MaxLength(20)]
    public string? PhoneNumber { get; set; }
    
    [Required]
    public UserRole Role { get; set; } = UserRole.User;
    
    public bool IsEmailConfirmed { get; set; } = false;
    
    public bool IsActive { get; set; } = true;
    
    public DateTime? LastLoginAt { get; set; }
    
    // Navigation properties
    public Guid FirmId { get; set; }
    public virtual Firm Firm { get; set; } = null!;
    
    public virtual ICollection<Site> CreatedSites { get; set; } = new List<Site>();
    public virtual ICollection<MaterialLog> MaterialLogs { get; set; } = new List<MaterialLog>();
    public virtual ICollection<Photo> Photos { get; set; } = new List<Photo>();
    
    // Computed properties
    public string FullName => $"{FirstName} {LastName}";
}

public enum UserRole
{
    User = 0,
    Admin = 1,
    SuperAdmin = 2
}
