using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using ConstructM.Domain.Common;

namespace ConstructM.Domain.Entities;

public class Estimate : BaseEntity
{
    [Required]
    [MaxLength(100)]
    public string Name { get; set; } = string.Empty;
    
    [MaxLength(500)]
    public string? Description { get; set; }
    
    [Required]
    public EstimateStatus Status { get; set; } = EstimateStatus.Draft;
    
    [Column(TypeName = "decimal(18,2)")]
    public decimal TotalCost { get; set; }
    
    [Column(TypeName = "decimal(18,2)")]
    public decimal LaborCost { get; set; }
    
    [Column(TypeName = "decimal(18,2)")]
    public decimal MaterialCost { get; set; }
    
    [Column(TypeName = "decimal(18,2)")]
    public decimal EquipmentCost { get; set; }
    
    [Column(TypeName = "decimal(18,2)")]
    public decimal OverheadCost { get; set; }
    
    [Column(TypeName = "decimal(18,2)")]
    public decimal ProfitMargin { get; set; }
    
    [Column(TypeName = "decimal(5,2)")]
    public decimal ProfitMarginPercentage { get; set; }
    
    public DateTime? ValidUntil { get; set; }
    
    [MaxLength(1000)]
    public string? Notes { get; set; }
    
    public string? EstimateData { get; set; } // JSON data for detailed breakdown
    
    // Navigation properties
    public Guid SiteId { get; set; }
    public virtual Site Site { get; set; } = null!;
    
    public virtual ICollection<EstimateItem> Items { get; set; } = new List<EstimateItem>();
}

public class EstimateItem : BaseEntity
{
    [Required]
    [MaxLength(200)]
    public string Name { get; set; } = string.Empty;
    
    [MaxLength(500)]
    public string? Description { get; set; }
    
    [Required]
    public EstimateItemCategory Category { get; set; }
    
    [Column(TypeName = "decimal(18,4)")]
    public decimal Quantity { get; set; }
    
    [MaxLength(20)]
    public string Unit { get; set; } = string.Empty;
    
    [Column(TypeName = "decimal(18,2)")]
    public decimal UnitCost { get; set; }
    
    [Column(TypeName = "decimal(18,2)")]
    public decimal TotalCost { get; set; }
    
    [MaxLength(500)]
    public string? Notes { get; set; }
    
    // Navigation properties
    public Guid EstimateId { get; set; }
    public virtual Estimate Estimate { get; set; } = null!;
}

public enum EstimateStatus
{
    Draft = 0,
    Pending = 1,
    Approved = 2,
    Rejected = 3,
    Expired = 4
}

public enum EstimateItemCategory
{
    Labor = 0,
    Material = 1,
    Equipment = 2,
    Subcontractor = 3,
    Overhead = 4,
    Other = 5
}
