using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using ConstructM.Domain.Entities;

namespace ConstructM.Infrastructure.Data.Configurations;

public class SiteConfiguration : IEntityTypeConfiguration<Site>
{
    public void Configure(EntityTypeBuilder<Site> builder)
    {
        builder.ToTable("Sites");

        builder.HasKey(s => s.Id);

        builder.Property(s => s.Name)
            .IsRequired()
            .HasMaxLength(200);

        builder.Property(s => s.Description)
            .HasMaxLength(1000);

        builder.Property(s => s.Location)
            .IsRequired()
            .HasMaxLength(500);

        builder.Property(s => s.Address)
            .HasMaxLength(500);

        builder.Property(s => s.City)
            .HasMaxLength(100);

        builder.Property(s => s.State)
            .HasMaxLength(100);

        builder.Property(s => s.PostalCode)
            .HasMaxLength(20);

        builder.Property(s => s.Area)
            .HasColumnType("decimal(18,2)");

        builder.Property(s => s.AreaUnit)
            .HasMaxLength(50)
            .HasDefaultValue("sqft");

        builder.Property(s => s.Type)
            .IsRequired()
            .HasConversion<int>();

        builder.Property(s => s.Status)
            .IsRequired()
            .HasConversion<int>();

        builder.Property(s => s.EstimatedBudget)
            .HasColumnType("decimal(18,2)");

        builder.Property(s => s.ActualCost)
            .HasColumnType("decimal(18,2)");

        builder.Property(s => s.DrawingUrl)
            .HasMaxLength(500);

        builder.Property(s => s.DrawingFileName)
            .HasMaxLength(100);

        builder.Property(s => s.Notes)
            .HasMaxLength(1000);

        // Relationships
        builder.HasOne(s => s.Firm)
            .WithMany(f => f.Sites)
            .HasForeignKey(s => s.FirmId)
            .OnDelete(DeleteBehavior.Restrict);

        builder.HasOne(s => s.CreatedByUser)
            .WithMany(u => u.CreatedSites)
            .HasForeignKey(s => s.CreatedByUserId)
            .OnDelete(DeleteBehavior.Restrict);

        builder.HasMany(s => s.Estimates)
            .WithOne(e => e.Site)
            .HasForeignKey(e => e.SiteId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(s => s.MaterialLogs)
            .WithOne(ml => ml.Site)
            .HasForeignKey(ml => ml.SiteId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(s => s.Photos)
            .WithOne(p => p.Site)
            .HasForeignKey(p => p.SiteId)
            .OnDelete(DeleteBehavior.Cascade);

        // Computed properties (ignored)
        builder.Ignore(s => s.BudgetVariance);
        builder.Ignore(s => s.BudgetVariancePercentage);

        // Indexes
        builder.HasIndex(s => s.FirmId);
        builder.HasIndex(s => s.Status);
        builder.HasIndex(s => s.Type);
    }
}
