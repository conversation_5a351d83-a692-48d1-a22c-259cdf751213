import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { AuthProvider } from '@/context/AuthContext';
import { ProtectedRoute } from '@/components/auth/ProtectedRoute';
import { Layout } from '@/components/layout/Layout';
import { LoginPage } from '@/pages/auth/LoginPage';
import { RegisterPage } from '@/pages/auth/RegisterPage';
import { DashboardPage } from '@/pages/DashboardPage';
import { SitesPage } from '@/pages/sites/SitesPage';
import { SiteDetailsPage } from '@/pages/sites/SiteDetailsPage';
import { CreateSitePage } from '@/pages/sites/CreateSitePage';
import { EditSitePage } from '@/pages/sites/EditSitePage';
import { MaterialsPage } from '@/pages/materials/MaterialsPage';
import { PhotosPage } from '@/pages/photos/PhotosPage';
import { ReportsPage } from '@/pages/reports/ReportsPage';
import { ErrorBoundary } from '@/components/common/ErrorBoundary';
import { ToastProvider, Toaster } from '@/components/ui/Toaster';

// Create a client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      refetchOnWindowFocus: false,
      staleTime: 5 * 60 * 1000, // 5 minutes
    },
  },
});

function App() {
  return (
    <ErrorBoundary>
      <QueryClientProvider client={queryClient}>
        <AuthProvider>
          <ToastProvider>
            <Router>
            <div className="min-h-screen bg-gray-50">
              <Routes>
                {/* Public routes */}
                <Route path="/login" element={<LoginPage />} />
                <Route path="/register" element={<RegisterPage />} />

                {/* Protected routes */}
                <Route
                  path="/*"
                  element={
                    <ProtectedRoute>
                      <Layout>
                        <Routes>
                          <Route path="/" element={<Navigate to="/dashboard" replace />} />
                          <Route path="/dashboard" element={<DashboardPage />} />

                          {/* Sites routes */}
                          <Route path="/sites" element={<SitesPage />} />
                          <Route path="/sites/new" element={<CreateSitePage />} />
                          <Route path="/sites/:id" element={<SiteDetailsPage />} />
                          <Route path="/sites/:id/edit" element={<EditSitePage />} />
                          <Route path="/sites/:id/materials" element={<MaterialsPage />} />
                          <Route path="/sites/:id/photos" element={<PhotosPage />} />
                          <Route path="/sites/:id/reports" element={<ReportsPage />} />

                          {/* Catch all */}
                          <Route path="*" element={<Navigate to="/dashboard" replace />} />
                        </Routes>
                      </Layout>
                    </ProtectedRoute>
                  }
                />
              </Routes>
            </div>
            <Toaster />
            </Router>
          </ToastProvider>
        </AuthProvider>
        <ReactQueryDevtools initialIsOpen={false} />
      </QueryClientProvider>
    </ErrorBoundary>
  );
}

export default App;
