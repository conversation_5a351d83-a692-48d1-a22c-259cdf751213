using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using ConstructM.Infrastructure.Data;
using ConstructM.Domain.Entities;
using System.Security.Claims;

namespace ConstructM.API.Controllers;

[ApiController]
[Route("api/sites/{siteId}/[controller]")]
[Authorize]
public class MaterialsController : ControllerBase
{
    private readonly ConstructMDbContext _context;
    private readonly ILogger<MaterialsController> _logger;

    public MaterialsController(ConstructMDbContext context, ILogger<MaterialsController> logger)
    {
        _context = context;
        _logger = logger;
    }

    [HttpGet]
    public async Task<IActionResult> GetMaterialLogs(Guid siteId, [FromQuery] DateTime? date, [FromQuery] MaterialCategory? category)
    {
        try
        {
            var firmId = GetCurrentUserFirmId();
            
            // Verify site belongs to user's firm
            var siteExists = await _context.Sites
                .AnyAsync(s => s.Id == siteId && s.FirmId == firmId);
            
            if (!siteExists)
            {
                return NotFound(new { message = "Site not found" });
            }

            var query = _context.MaterialLogs
                .Where(ml => ml.SiteId == siteId);

            if (date.HasValue)
            {
                var dateOnly = date.Value.Date;
                query = query.Where(ml => ml.LogDate.Date == dateOnly);
            }

            if (category.HasValue)
            {
                var categoryValue = category.Value;
                query = query.Where(ml => ml.Category == categoryValue);
            }

            query = query.Include(ml => ml.LoggedByUser);

            var materialLogs = await query
                .OrderByDescending(ml => ml.LogDate)
                .Select(ml => new
                {
                    ml.Id,
                    ml.MaterialName,
                    ml.Description,
                    ml.Category,
                    ml.Quantity,
                    ml.Unit,
                    ml.UnitRate,
                    ml.TotalCost,
                    ml.LogDate,
                    ml.LogType,
                    ml.Supplier,
                    ml.InvoiceNumber,
                    ml.Notes,
                    ml.ReceiptUrl,
                    ml.CreatedAt,
                    LoggedBy = ml.LoggedByUser.FullName
                })
                .ToListAsync();

            return Ok(materialLogs);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving material logs for site {SiteId}", siteId);
            return StatusCode(500, new { message = "An error occurred while retrieving material logs" });
        }
    }

    [HttpPost]
    public async Task<IActionResult> CreateMaterialLog(Guid siteId, [FromBody] CreateMaterialLogRequest request)
    {
        try
        {
            var firmId = GetCurrentUserFirmId();
            var userId = GetCurrentUserId();
            
            // Verify site belongs to user's firm
            var siteExists = await _context.Sites
                .AnyAsync(s => s.Id == siteId && s.FirmId == firmId);
            
            if (!siteExists)
            {
                return NotFound(new { message = "Site not found" });
            }

            var materialLog = new MaterialLog
            {
                MaterialName = request.MaterialName,
                Description = request.Description,
                Category = request.Category,
                Quantity = request.Quantity,
                Unit = request.Unit,
                UnitRate = request.UnitRate,
                TotalCost = request.Quantity * request.UnitRate,
                LogDate = request.LogDate ?? DateTime.UtcNow,
                LogType = request.LogType,
                Supplier = request.Supplier,
                InvoiceNumber = request.InvoiceNumber,
                Notes = request.Notes,
                SiteId = siteId,
                LoggedByUserId = userId
            };

            _context.MaterialLogs.Add(materialLog);
            await _context.SaveChangesAsync();

            return CreatedAtAction(nameof(GetMaterialLog), new { siteId, id = materialLog.Id }, 
                new { materialLog.Id, message = "Material log created successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating material log for site {SiteId}", siteId);
            return StatusCode(500, new { message = "An error occurred while creating the material log" });
        }
    }

    [HttpGet("{id}")]
    public async Task<IActionResult> GetMaterialLog(Guid siteId, Guid id)
    {
        try
        {
            var firmId = GetCurrentUserFirmId();
            
            var materialLog = await _context.MaterialLogs
                .Where(ml => ml.Id == id && ml.SiteId == siteId)
                .Include(ml => ml.Site)
                .Include(ml => ml.LoggedByUser)
                .Where(ml => ml.Site.FirmId == firmId)
                .Select(ml => new
                {
                    ml.Id,
                    ml.MaterialName,
                    ml.Description,
                    ml.Category,
                    ml.Quantity,
                    ml.Unit,
                    ml.UnitRate,
                    ml.TotalCost,
                    ml.LogDate,
                    ml.LogType,
                    ml.Supplier,
                    ml.InvoiceNumber,
                    ml.Notes,
                    ml.ReceiptUrl,
                    ml.CreatedAt,
                    ml.UpdatedAt,
                    LoggedBy = ml.LoggedByUser.FullName,
                    SiteName = ml.Site.Name
                })
                .FirstOrDefaultAsync();

            if (materialLog == null)
            {
                return NotFound(new { message = "Material log not found" });
            }

            return Ok(materialLog);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving material log {MaterialLogId} for site {SiteId}", id, siteId);
            return StatusCode(500, new { message = "An error occurred while retrieving the material log" });
        }
    }

    [HttpPut("{id}")]
    public async Task<IActionResult> UpdateMaterialLog(Guid siteId, Guid id, [FromBody] UpdateMaterialLogRequest request)
    {
        try
        {
            var firmId = GetCurrentUserFirmId();
            
            var materialLog = await _context.MaterialLogs
                .Include(ml => ml.Site)
                .FirstOrDefaultAsync(ml => ml.Id == id && ml.SiteId == siteId && ml.Site.FirmId == firmId);

            if (materialLog == null)
            {
                return NotFound(new { message = "Material log not found" });
            }

            materialLog.MaterialName = request.MaterialName;
            materialLog.Description = request.Description;
            materialLog.Category = request.Category;
            materialLog.Quantity = request.Quantity;
            materialLog.Unit = request.Unit;
            materialLog.UnitRate = request.UnitRate;
            materialLog.TotalCost = request.Quantity * request.UnitRate;
            materialLog.LogDate = request.LogDate ?? materialLog.LogDate;
            materialLog.LogType = request.LogType;
            materialLog.Supplier = request.Supplier;
            materialLog.InvoiceNumber = request.InvoiceNumber;
            materialLog.Notes = request.Notes;

            await _context.SaveChangesAsync();

            return Ok(new { message = "Material log updated successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating material log {MaterialLogId} for site {SiteId}", id, siteId);
            return StatusCode(500, new { message = "An error occurred while updating the material log" });
        }
    }

    [HttpDelete("{id}")]
    public async Task<IActionResult> DeleteMaterialLog(Guid siteId, Guid id)
    {
        try
        {
            var firmId = GetCurrentUserFirmId();
            
            var materialLog = await _context.MaterialLogs
                .Include(ml => ml.Site)
                .FirstOrDefaultAsync(ml => ml.Id == id && ml.SiteId == siteId && ml.Site.FirmId == firmId);

            if (materialLog == null)
            {
                return NotFound(new { message = "Material log not found" });
            }

            _context.MaterialLogs.Remove(materialLog);
            await _context.SaveChangesAsync();

            return Ok(new { message = "Material log deleted successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting material log {MaterialLogId} for site {SiteId}", id, siteId);
            return StatusCode(500, new { message = "An error occurred while deleting the material log" });
        }
    }

    private Guid GetCurrentUserFirmId()
    {
        var firmIdClaim = User.FindFirst("FirmId")?.Value;
        return Guid.Parse(firmIdClaim ?? throw new UnauthorizedAccessException("Firm ID not found in token"));
    }

    private Guid GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        return Guid.Parse(userIdClaim ?? throw new UnauthorizedAccessException("User ID not found in token"));
    }
}

public class CreateMaterialLogRequest
{
    public string MaterialName { get; set; } = string.Empty;
    public string? Description { get; set; }
    public MaterialCategory Category { get; set; }
    public decimal Quantity { get; set; }
    public string Unit { get; set; } = string.Empty;
    public decimal UnitRate { get; set; }
    public DateTime? LogDate { get; set; }
    public MaterialLogType LogType { get; set; }
    public string? Supplier { get; set; }
    public string? InvoiceNumber { get; set; }
    public string? Notes { get; set; }
}

public class UpdateMaterialLogRequest : CreateMaterialLogRequest
{
}
