import React from 'react';
import { useParams, Link } from 'react-router-dom';
import { useSite } from '@/hooks/useSites';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { SiteType, SiteStatus } from '@/types';

export const SiteDetailsPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const { data: site, isLoading, error } = useSite(id!);

  const getSiteTypeLabel = (type: SiteType): string => {
    const labels = {
      [SiteType.Residential]: 'Residential',
      [SiteType.Commercial]: 'Commercial',
      [SiteType.Industrial]: 'Industrial',
      [SiteType.Infrastructure]: 'Infrastructure',
      [SiteType.Renovation]: 'Renovation',
      [SiteType.Other]: 'Other',
    };
    return labels[type] || 'Unknown';
  };

  const getSiteStatusLabel = (status: SiteStatus): string => {
    const labels = {
      [SiteStatus.Planning]: 'Planning',
      [SiteStatus.InProgress]: 'In Progress',
      [SiteStatus.OnHold]: 'On Hold',
      [SiteStatus.Completed]: 'Completed',
      [SiteStatus.Cancelled]: 'Cancelled',
    };
    return labels[status] || 'Unknown';
  };

  const getStatusColor = (status: SiteStatus): string => {
    const colors = {
      [SiteStatus.Planning]: 'bg-yellow-100 text-yellow-800',
      [SiteStatus.InProgress]: 'bg-blue-100 text-blue-800',
      [SiteStatus.OnHold]: 'bg-gray-100 text-gray-800',
      [SiteStatus.Completed]: 'bg-green-100 text-green-800',
      [SiteStatus.Cancelled]: 'bg-red-100 text-red-800',
    };
    return colors[status] || 'bg-gray-100 text-gray-800';
  };

  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (error || !site) {
    return (
      <div className="text-center py-12">
        <div className="text-red-600 mb-4">
          <svg className="mx-auto h-12 w-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
          </svg>
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">Site not found</h3>
        <p className="text-gray-500 mb-4">The site you're looking for doesn't exist or you don't have access to it.</p>
        <Link to="/sites" className="btn-primary">
          Back to Sites
        </Link>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="md:flex md:items-center md:justify-between">
        <div className="flex-1 min-w-0">
          <div className="flex items-center">
            <Link
              to="/sites"
              className="mr-4 text-gray-400 hover:text-gray-600"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </Link>
            <div>
              <h2 className="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
                {site.name}
              </h2>
              <div className="mt-1 flex flex-col sm:flex-row sm:flex-wrap sm:space-x-6">
                <div className="mt-2 flex items-center text-sm text-gray-500">
                  <svg className="flex-shrink-0 mr-1.5 h-5 w-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                  </svg>
                  {site.location}
                </div>
                <div className="mt-2 flex items-center text-sm text-gray-500">
                  <svg className="flex-shrink-0 mr-1.5 h-5 w-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
                  </svg>
                  {getSiteTypeLabel(site.type)}
                </div>
                <div className="mt-2 flex items-center">
                  <span
                    className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(
                      site.status
                    )}`}
                  >
                    {getSiteStatusLabel(site.status)}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="mt-4 flex md:mt-0 md:ml-4 space-x-3">
          <Link
            to={`/sites/${site.id}/edit`}
            className="btn-secondary"
          >
            Edit Site
          </Link>
          <Link
            to={`/sites/${site.id}/materials`}
            className="btn-outline"
          >
            Materials
          </Link>
          <Link
            to={`/sites/${site.id}/photos`}
            className="btn-outline"
          >
            Photos
          </Link>
          <Link
            to={`/sites/${site.id}/reports`}
            className="btn-primary"
          >
            Reports
          </Link>
        </div>
      </div>

      {/* Overview Cards */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        <div className="card">
          <div className="card-content">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                  <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                  </svg>
                </div>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    Budget
                  </dt>
                  <dd className="text-lg font-medium text-gray-900">
                    {site.estimatedBudget ? formatCurrency(site.estimatedBudget) : 'Not set'}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="card">
          <div className="card-content">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-red-500 rounded-md flex items-center justify-center">
                  <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                  </svg>
                </div>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    Spent
                  </dt>
                  <dd className="text-lg font-medium text-gray-900">
                    {site.actualCost ? formatCurrency(site.actualCost) : '$0'}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="card">
          <div className="card-content">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                  <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z" />
                  </svg>
                </div>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    Materials
                  </dt>
                  <dd className="text-lg font-medium text-gray-900">
                    {site.materialLogsCount || 0}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="card">
          <div className="card-content">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                  <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd" />
                  </svg>
                </div>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    Photos
                  </dt>
                  <dd className="text-lg font-medium text-gray-900">
                    {site.photosCount || 0}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Site Information */}
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-medium text-gray-900">Site Information</h3>
          </div>
          <div className="card-content">
            <dl className="space-y-4">
              {site.description && (
                <div>
                  <dt className="text-sm font-medium text-gray-500">Description</dt>
                  <dd className="mt-1 text-sm text-gray-900">{site.description}</dd>
                </div>
              )}

              <div>
                <dt className="text-sm font-medium text-gray-500">Location</dt>
                <dd className="mt-1 text-sm text-gray-900">{site.location}</dd>
              </div>

              {site.address && (
                <div>
                  <dt className="text-sm font-medium text-gray-500">Address</dt>
                  <dd className="mt-1 text-sm text-gray-900">
                    {site.address}
                    {site.city && `, ${site.city}`}
                    {site.state && `, ${site.state}`}
                    {site.postalCode && ` ${site.postalCode}`}
                  </dd>
                </div>
              )}

              {site.area && (
                <div>
                  <dt className="text-sm font-medium text-gray-500">Area</dt>
                  <dd className="mt-1 text-sm text-gray-900">
                    {site.area.toLocaleString()} {site.areaUnit}
                  </dd>
                </div>
              )}

              <div>
                <dt className="text-sm font-medium text-gray-500">Created By</dt>
                <dd className="mt-1 text-sm text-gray-900">{site.createdBy}</dd>
              </div>

              <div>
                <dt className="text-sm font-medium text-gray-500">Created</dt>
                <dd className="mt-1 text-sm text-gray-900">{formatDate(site.createdAt)}</dd>
              </div>
            </dl>
          </div>
        </div>

        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-medium text-gray-900">Project Timeline</h3>
          </div>
          <div className="card-content">
            <dl className="space-y-4">
              {site.startDate && (
                <div>
                  <dt className="text-sm font-medium text-gray-500">Start Date</dt>
                  <dd className="mt-1 text-sm text-gray-900">{formatDate(site.startDate)}</dd>
                </div>
              )}

              {site.expectedEndDate && (
                <div>
                  <dt className="text-sm font-medium text-gray-500">Expected End Date</dt>
                  <dd className="mt-1 text-sm text-gray-900">{formatDate(site.expectedEndDate)}</dd>
                </div>
              )}

              {site.actualEndDate && (
                <div>
                  <dt className="text-sm font-medium text-gray-500">Actual End Date</dt>
                  <dd className="mt-1 text-sm text-gray-900">{formatDate(site.actualEndDate)}</dd>
                </div>
              )}

              {site.estimatedBudget && site.actualCost && (
                <div>
                  <dt className="text-sm font-medium text-gray-500">Budget Variance</dt>
                  <dd className="mt-1 text-sm">
                    <span className={`font-medium ${site.budgetVariance >= 0 ? 'text-red-600' : 'text-green-600'}`}>
                      {site.budgetVariance >= 0 ? '+' : ''}{formatCurrency(site.budgetVariance)}
                    </span>
                    <span className="text-gray-500 ml-2">
                      ({site.budgetVariancePercentage >= 0 ? '+' : ''}{site.budgetVariancePercentage.toFixed(1)}%)
                    </span>
                  </dd>
                </div>
              )}

              {site.notes && (
                <div>
                  <dt className="text-sm font-medium text-gray-500">Notes</dt>
                  <dd className="mt-1 text-sm text-gray-900">{site.notes}</dd>
                </div>
              )}
            </dl>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="card">
        <div className="card-header">
          <h3 className="text-lg font-medium text-gray-900">Quick Actions</h3>
        </div>
        <div className="card-content">
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
            <Link
              to={`/sites/${site.id}/materials`}
              className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <div className="flex-shrink-0">
                <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                  <svg className="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z" />
                  </svg>
                </div>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-900">Manage Materials</p>
                <p className="text-sm text-gray-500">Track material usage and costs</p>
              </div>
            </Link>

            <Link
              to={`/sites/${site.id}/photos`}
              className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <div className="flex-shrink-0">
                <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                  <svg className="w-6 h-6 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd" />
                  </svg>
                </div>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-900">Progress Photos</p>
                <p className="text-sm text-gray-500">Upload and organize site photos</p>
              </div>
            </Link>

            <Link
              to={`/sites/${site.id}/reports`}
              className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <div className="flex-shrink-0">
                <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                  <svg className="w-6 h-6 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z" />
                  </svg>
                </div>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-900">View Reports</p>
                <p className="text-sm text-gray-500">Analytics and cost reports</p>
              </div>
            </Link>

            <Link
              to={`/sites/${site.id}/edit`}
              className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <div className="flex-shrink-0">
                <div className="w-10 h-10 bg-yellow-100 rounded-lg flex items-center justify-center">
                  <svg className="w-6 h-6 text-yellow-600" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
                  </svg>
                </div>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-900">Edit Site</p>
                <p className="text-sm text-gray-500">Update site information</p>
              </div>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};
