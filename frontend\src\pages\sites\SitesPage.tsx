import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { useSites } from '@/hooks/useSites';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { SiteType, SiteStatus } from '@/types';

export const SitesPage: React.FC = () => {
  const { data: sites, isLoading, error } = useSites();
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<SiteStatus | 'all'>('all');
  const [typeFilter, setTypeFilter] = useState<SiteType | 'all'>('all');

  const getSiteTypeLabel = (type: SiteType): string => {
    const labels = {
      [SiteType.Residential]: 'Residential',
      [SiteType.Commercial]: 'Commercial',
      [SiteType.Industrial]: 'Industrial',
      [SiteType.Infrastructure]: 'Infrastructure',
      [SiteType.Renovation]: 'Renovation',
      [SiteType.Other]: 'Other',
    };
    return labels[type] || 'Unknown';
  };

  const getSiteStatusLabel = (status: SiteStatus): string => {
    const labels = {
      [SiteStatus.Planning]: 'Planning',
      [SiteStatus.InProgress]: 'In Progress',
      [SiteStatus.OnHold]: 'On Hold',
      [SiteStatus.Completed]: 'Completed',
      [SiteStatus.Cancelled]: 'Cancelled',
    };
    return labels[status] || 'Unknown';
  };

  const getStatusColor = (status: SiteStatus): string => {
    const colors = {
      [SiteStatus.Planning]: 'bg-yellow-100 text-yellow-800',
      [SiteStatus.InProgress]: 'bg-blue-100 text-blue-800',
      [SiteStatus.OnHold]: 'bg-gray-100 text-gray-800',
      [SiteStatus.Completed]: 'bg-green-100 text-green-800',
      [SiteStatus.Cancelled]: 'bg-red-100 text-red-800',
    };
    return colors[status] || 'bg-gray-100 text-gray-800';
  };

  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  // Filter sites based on search and filters
  const filteredSites = sites?.filter((site) => {
    const matchesSearch = searchQuery === '' || 
      site.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      site.location.toLowerCase().includes(searchQuery.toLowerCase()) ||
      site.description?.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesStatus = statusFilter === 'all' || site.status === statusFilter;
    const matchesType = typeFilter === 'all' || site.type === typeFilter;

    return matchesSearch && matchesStatus && matchesType;
  }) || [];

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <div className="text-red-600 mb-4">
          <svg className="mx-auto h-12 w-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
          </svg>
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">Error loading sites</h3>
        <p className="text-gray-500">Please try refreshing the page.</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="md:flex md:items-center md:justify-between">
        <div className="flex-1 min-w-0">
          <h2 className="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
            Construction Sites
          </h2>
          <p className="mt-1 text-sm text-gray-500">
            Manage all your construction projects in one place.
          </p>
        </div>
        <div className="mt-4 flex md:mt-0 md:ml-4">
          <Link
            to="/sites/new"
            className="btn-primary"
          >
            New Site
          </Link>
        </div>
      </div>

      {/* Filters */}
      <div className="card">
        <div className="card-content">
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-3">
            <div>
              <label htmlFor="search" className="block text-sm font-medium text-gray-700">
                Search
              </label>
              <input
                type="text"
                id="search"
                className="mt-1 input"
                placeholder="Search sites..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>

            <div>
              <label htmlFor="status" className="block text-sm font-medium text-gray-700">
                Status
              </label>
              <select
                id="status"
                className="mt-1 input"
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value as SiteStatus | 'all')}
              >
                <option value="all">All Statuses</option>
                <option value={SiteStatus.Planning}>Planning</option>
                <option value={SiteStatus.InProgress}>In Progress</option>
                <option value={SiteStatus.OnHold}>On Hold</option>
                <option value={SiteStatus.Completed}>Completed</option>
                <option value={SiteStatus.Cancelled}>Cancelled</option>
              </select>
            </div>

            <div>
              <label htmlFor="type" className="block text-sm font-medium text-gray-700">
                Type
              </label>
              <select
                id="type"
                className="mt-1 input"
                value={typeFilter}
                onChange={(e) => setTypeFilter(e.target.value as SiteType | 'all')}
              >
                <option value="all">All Types</option>
                <option value={SiteType.Residential}>Residential</option>
                <option value={SiteType.Commercial}>Commercial</option>
                <option value={SiteType.Industrial}>Industrial</option>
                <option value={SiteType.Infrastructure}>Infrastructure</option>
                <option value={SiteType.Renovation}>Renovation</option>
                <option value={SiteType.Other}>Other</option>
              </select>
            </div>
          </div>
        </div>
      </div>

      {/* Sites Grid */}
      {filteredSites.length > 0 ? (
        <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
          {filteredSites.map((site) => (
            <div key={site.id} className="card hover:shadow-md transition-shadow">
              <div className="card-content">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    <div className="h-10 w-10 bg-primary-100 rounded-lg flex items-center justify-center">
                      <span className="text-primary-600 font-medium text-sm">
                        {site.name.charAt(0)}
                      </span>
                    </div>
                    <div>
                      <h3 className="text-lg font-medium text-gray-900 truncate">
                        {site.name}
                      </h3>
                      <p className="text-sm text-gray-500">{getSiteTypeLabel(site.type)}</p>
                    </div>
                  </div>
                  <span
                    className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(
                      site.status
                    )}`}
                  >
                    {getSiteStatusLabel(site.status)}
                  </span>
                </div>

                <div className="space-y-3">
                  <div>
                    <p className="text-sm text-gray-600">
                      <span className="font-medium">Location:</span> {site.location}
                    </p>
                    {site.description && (
                      <p className="text-sm text-gray-600 mt-1 line-clamp-2">
                        {site.description}
                      </p>
                    )}
                  </div>

                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <p className="text-gray-500">Budget</p>
                      <p className="font-medium text-gray-900">
                        {site.estimatedBudget ? formatCurrency(site.estimatedBudget) : 'N/A'}
                      </p>
                    </div>
                    <div>
                      <p className="text-gray-500">Spent</p>
                      <p className="font-medium text-gray-900">
                        {site.actualCost ? formatCurrency(site.actualCost) : '$0'}
                      </p>
                    </div>
                  </div>

                  {site.startDate && (
                    <div className="text-sm">
                      <p className="text-gray-500">Started</p>
                      <p className="font-medium text-gray-900">
                        {formatDate(site.startDate)}
                      </p>
                    </div>
                  )}

                  <div className="flex items-center justify-between pt-4 border-t border-gray-200">
                    <div className="flex space-x-4 text-sm text-gray-500">
                      <span>{site.materialLogsCount || 0} materials</span>
                      <span>{site.photosCount || 0} photos</span>
                    </div>
                    <Link
                      to={`/sites/${site.id}`}
                      className="text-primary-600 hover:text-primary-500 font-medium text-sm"
                    >
                      View Details
                    </Link>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <svg
            className="mx-auto h-12 w-12 text-gray-400"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
            />
          </svg>
          <h3 className="mt-2 text-sm font-medium text-gray-900">
            {searchQuery || statusFilter !== 'all' || typeFilter !== 'all'
              ? 'No sites match your filters'
              : 'No sites found'}
          </h3>
          <p className="mt-1 text-sm text-gray-500">
            {searchQuery || statusFilter !== 'all' || typeFilter !== 'all'
              ? 'Try adjusting your search criteria.'
              : 'Get started by creating your first construction site.'}
          </p>
          {!searchQuery && statusFilter === 'all' && typeFilter === 'all' && (
            <div className="mt-6">
              <Link
                to="/sites/new"
                className="btn-primary"
              >
                New Site
              </Link>
            </div>
          )}
        </div>
      )}
    </div>
  );
};
