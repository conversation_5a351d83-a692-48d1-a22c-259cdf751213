import React from 'react';
import { Link } from 'react-router-dom';
import { MapPin, Calendar, DollarSign, TrendingUp, AlertTriangle } from 'lucide-react';
import { <PERSON>, Card<PERSON>eader, CardTitle, CardContent } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';
import { Button } from '@/components/ui/Button';
import { SiteType, SiteStatus } from '@/types';

interface Site {
  id: string;
  name: string;
  location: string;
  type: SiteType;
  status: SiteStatus;
  estimatedBudget?: number;
  actualCost?: number;
  startDate?: string;
  expectedEndDate?: string;
  progress?: number;
}

interface ProjectOverviewProps {
  sites: Site[];
}

export const ProjectOverview: React.FC<ProjectOverviewProps> = ({ sites }) => {
  const getSiteStatusLabel = (status: SiteStatus): string => {
    const labels = {
      [SiteStatus.Planning]: 'Planning',
      [SiteStatus.InProgress]: 'In Progress',
      [SiteStatus.OnHold]: 'On Hold',
      [SiteStatus.Completed]: 'Completed',
      [SiteStatus.Cancelled]: 'Cancelled',
    };
    return labels[status] || 'Unknown';
  };

  const getStatusColor = (status: SiteStatus): 'success' | 'warning' | 'error' | 'info' | 'default' => {
    const colors = {
      [SiteStatus.Planning]: 'warning' as const,
      [SiteStatus.InProgress]: 'info' as const,
      [SiteStatus.OnHold]: 'default' as const,
      [SiteStatus.Completed]: 'success' as const,
      [SiteStatus.Cancelled]: 'error' as const,
    };
    return colors[status] || 'default';
  };

  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
    });
  };

  const calculateProgress = (site: Site): number => {
    if (site.progress) return site.progress;
    if (site.status === SiteStatus.Completed) return 100;
    if (site.status === SiteStatus.InProgress) return Math.floor(Math.random() * 60) + 20; // Mock progress
    return 0;
  };

  const getBudgetStatus = (site: Site): { status: 'good' | 'warning' | 'danger'; message: string } => {
    if (!site.estimatedBudget || !site.actualCost) {
      return { status: 'good', message: 'On track' };
    }

    const percentage = (site.actualCost / site.estimatedBudget) * 100;
    
    if (percentage > 100) {
      return { status: 'danger', message: 'Over budget' };
    } else if (percentage > 80) {
      return { status: 'warning', message: 'Near budget' };
    } else {
      return { status: 'good', message: 'On track' };
    }
  };

  // Mock data if no sites provided
  const displaySites = sites.length > 0 ? sites.slice(0, 6) : [
    {
      id: '1',
      name: 'Downtown Office Complex',
      location: 'Downtown District',
      type: SiteType.Commercial,
      status: SiteStatus.InProgress,
      estimatedBudget: 850000,
      actualCost: 620000,
      startDate: '2024-01-15',
      expectedEndDate: '2024-06-30',
      progress: 73,
    },
    {
      id: '2',
      name: 'Residential Towers',
      location: 'Westside',
      type: SiteType.Residential,
      status: SiteStatus.InProgress,
      estimatedBudget: 1200000,
      actualCost: 890000,
      startDate: '2023-11-01',
      expectedEndDate: '2024-08-15',
      progress: 65,
    },
    {
      id: '3',
      name: 'Shopping Mall Renovation',
      location: 'City Center',
      type: SiteType.Renovation,
      status: SiteStatus.Planning,
      estimatedBudget: 450000,
      actualCost: 0,
      startDate: '2024-03-01',
      expectedEndDate: '2024-07-30',
      progress: 0,
    },
  ];

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          Project Overview
          <Link to="/sites">
            <Button variant="outline" size="sm">
              View All
            </Button>
          </Link>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {displaySites.map((site) => {
            const progress = calculateProgress(site);
            const budgetStatus = getBudgetStatus(site);
            
            return (
              <div
                key={site.id}
                className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
              >
                <div className="flex items-start justify-between mb-3">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-1">
                      <h4 className="font-medium text-gray-900 truncate">
                        {site.name}
                      </h4>
                      <Badge variant={getStatusColor(site.status)} size="sm">
                        {getSiteStatusLabel(site.status)}
                      </Badge>
                    </div>
                    <div className="flex items-center text-sm text-gray-500 space-x-4">
                      <div className="flex items-center">
                        <MapPin className="w-3 h-3 mr-1" />
                        <span className="truncate">{site.location}</span>
                      </div>
                      {site.expectedEndDate && (
                        <div className="flex items-center">
                          <Calendar className="w-3 h-3 mr-1" />
                          <span>Due {formatDate(site.expectedEndDate)}</span>
                        </div>
                      )}
                    </div>
                  </div>
                  <Link
                    to={`/sites/${site.id}`}
                    className="text-blue-600 hover:text-blue-500 text-sm font-medium"
                  >
                    View
                  </Link>
                </div>

                {/* Progress Bar */}
                <div className="mb-3">
                  <div className="flex items-center justify-between text-sm mb-1">
                    <span className="text-gray-600">Progress</span>
                    <span className="font-medium text-gray-900">{progress}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className={`h-2 rounded-full transition-all duration-300 ${
                        progress >= 80
                          ? 'bg-green-500'
                          : progress >= 50
                          ? 'bg-blue-500'
                          : 'bg-yellow-500'
                      }`}
                      style={{ width: `${progress}%` }}
                    />
                  </div>
                </div>

                {/* Budget Information */}
                {site.estimatedBudget && (
                  <div className="flex items-center justify-between text-sm">
                    <div className="flex items-center space-x-4">
                      <div className="flex items-center">
                        <DollarSign className="w-3 h-3 mr-1 text-gray-400" />
                        <span className="text-gray-600">
                          {formatCurrency(site.actualCost || 0)} / {formatCurrency(site.estimatedBudget)}
                        </span>
                      </div>
                    </div>
                    <div className="flex items-center space-x-1">
                      {budgetStatus.status === 'good' && (
                        <TrendingUp className="w-3 h-3 text-green-500" />
                      )}
                      {budgetStatus.status === 'warning' && (
                        <AlertTriangle className="w-3 h-3 text-yellow-500" />
                      )}
                      {budgetStatus.status === 'danger' && (
                        <AlertTriangle className="w-3 h-3 text-red-500" />
                      )}
                      <span
                        className={`text-xs font-medium ${
                          budgetStatus.status === 'good'
                            ? 'text-green-600'
                            : budgetStatus.status === 'warning'
                            ? 'text-yellow-600'
                            : 'text-red-600'
                        }`}
                      >
                        {budgetStatus.message}
                      </span>
                    </div>
                  </div>
                )}
              </div>
            );
          })}
        </div>

        {displaySites.length === 0 && (
          <div className="text-center py-6">
            <div className="mx-auto h-12 w-12 text-gray-400 mb-4">
              <svg fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
                />
              </svg>
            </div>
            <h3 className="text-sm font-medium text-gray-900">No projects</h3>
            <p className="mt-1 text-sm text-gray-500">
              Get started by creating your first construction site.
            </p>
            <div className="mt-4">
              <Link to="/sites/new">
                <Button size="sm">
                  Create Project
                </Button>
              </Link>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
