import React from 'react';
import { Package, Camera, Edit, User, Clock } from 'lucide-react';
import { <PERSON>, CardHeader, CardT<PERSON>le, CardContent } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';

interface Activity {
  id: string;
  type: string;
  message: string;
  user: string;
  timestamp: string;
  icon: string;
}

interface RecentActivityProps {
  activities: Activity[];
}

export const RecentActivity: React.FC<RecentActivityProps> = ({ activities }) => {
  const getIcon = (iconType: string) => {
    const icons = {
      package: Package,
      camera: Camera,
      edit: Edit,
      user: User,
    };
    const IconComponent = icons[iconType as keyof typeof icons] || Clock;
    return <IconComponent className="w-5 h-5" />;
  };

  const getIconColor = (type: string): string => {
    const colors = {
      material_added: 'bg-blue-100 text-blue-600',
      photo_uploaded: 'bg-purple-100 text-purple-600',
      site_updated: 'bg-green-100 text-green-600',
      user_joined: 'bg-yellow-100 text-yellow-600',
    };
    return colors[type as keyof typeof colors] || 'bg-gray-100 text-gray-600';
  };

  const getActivityBadge = (type: string): { variant: 'success' | 'info' | 'warning' | 'default'; label: string } => {
    const badges = {
      material_added: { variant: 'info' as const, label: 'Material' },
      photo_uploaded: { variant: 'success' as const, label: 'Photo' },
      site_updated: { variant: 'warning' as const, label: 'Update' },
      user_joined: { variant: 'default' as const, label: 'User' },
    };
    return badges[type as keyof typeof badges] || { variant: 'default', label: 'Activity' };
  };

  const formatTimeAgo = (timestamp: string): string => {
    const now = new Date();
    const time = new Date(timestamp);
    const diffInMinutes = Math.floor((now.getTime() - time.getTime()) / (1000 * 60));

    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    
    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `${diffInHours}h ago`;
    
    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) return `${diffInDays}d ago`;
    
    return time.toLocaleDateString();
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          Recent Activity
          <Badge variant="default" size="sm">
            {activities.length} updates
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="flow-root">
          <ul className="-mb-8">
            {activities.map((activity, index) => {
              const badge = getActivityBadge(activity.type);
              return (
                <li key={activity.id}>
                  <div className="relative pb-8">
                    {index !== activities.length - 1 && (
                      <span
                        className="absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200"
                        aria-hidden="true"
                      />
                    )}
                    <div className="relative flex space-x-3">
                      <div>
                        <span
                          className={`h-8 w-8 rounded-full flex items-center justify-center ring-8 ring-white ${getIconColor(
                            activity.type
                          )}`}
                        >
                          {getIcon(activity.icon)}
                        </span>
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-2">
                            <Badge variant={badge.variant} size="sm">
                              {badge.label}
                            </Badge>
                            <span className="text-xs text-gray-500">
                              {formatTimeAgo(activity.timestamp)}
                            </span>
                          </div>
                        </div>
                        <div className="mt-1">
                          <p className="text-sm text-gray-900">{activity.message}</p>
                          <div className="mt-1 flex items-center space-x-2">
                            <User className="w-3 h-3 text-gray-400" />
                            <span className="text-xs text-gray-500">{activity.user}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </li>
              );
            })}
          </ul>
        </div>
        
        {activities.length === 0 && (
          <div className="text-center py-6">
            <Clock className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No recent activity</h3>
            <p className="mt-1 text-sm text-gray-500">
              Activity will appear here as team members work on projects.
            </p>
          </div>
        )}

        <div className="mt-6 pt-4 border-t border-gray-200">
          <button className="w-full text-center text-sm text-blue-600 hover:text-blue-500 font-medium">
            View all activity
          </button>
        </div>
      </CardContent>
    </Card>
  );
};
