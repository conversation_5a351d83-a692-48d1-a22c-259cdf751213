import { apiClient } from './api';
import type { MaterialLog, CreateMaterialLogRequest, MaterialCategory, MaterialLogType } from '@/types';

export const materialsService = {
  // Get all material logs for a site
  getAll: async (siteId: string, filters?: {
    date?: string;
    category?: MaterialCategory;
  }): Promise<MaterialLog[]> => {
    const params = new URLSearchParams();
    if (filters?.date) params.append('date', filters.date);
    if (filters?.category !== undefined) params.append('category', filters.category.toString());
    
    const url = `/sites/${siteId}/materials${params.toString() ? `?${params.toString()}` : ''}`;
    const response = await apiClient.get<MaterialLog[]>(url);
    return response.data;
  },

  // Get a specific material log
  getById: async (siteId: string, id: string): Promise<MaterialLog> => {
    const response = await apiClient.get<MaterialLog>(`/sites/${siteId}/materials/${id}`);
    return response.data;
  },

  // Create a new material log
  create: async (siteId: string, data: CreateMaterialLogRequest): Promise<{ id: string; message: string }> => {
    const response = await apiClient.post<{ id: string; message: string }>(`/sites/${siteId}/materials`, data);
    return response.data;
  },

  // Update a material log
  update: async (siteId: string, id: string, data: CreateMaterialLogRequest): Promise<{ message: string }> => {
    const response = await apiClient.put<{ message: string }>(`/sites/${siteId}/materials/${id}`, data);
    return response.data;
  },

  // Delete a material log
  delete: async (siteId: string, id: string): Promise<{ message: string }> => {
    const response = await apiClient.delete<{ message: string }>(`/sites/${siteId}/materials/${id}`);
    return response.data;
  },

  // Get material statistics for a site
  getStatistics: async (siteId: string): Promise<{
    totalCost: number;
    totalItems: number;
    costByCategory: Array<{ category: string; cost: number; count: number }>;
    recentLogs: MaterialLog[];
  }> => {
    const logs = await materialsService.getAll(siteId);
    
    const totalCost = logs.reduce((sum, log) => sum + log.totalCost, 0);
    const totalItems = logs.length;
    
    // Group by category
    const categoryMap = new Map<MaterialCategory, { cost: number; count: number }>();
    logs.forEach(log => {
      const existing = categoryMap.get(log.category) || { cost: 0, count: 0 };
      categoryMap.set(log.category, {
        cost: existing.cost + log.totalCost,
        count: existing.count + 1,
      });
    });
    
    const costByCategory = Array.from(categoryMap.entries()).map(([category, data]) => ({
      category: MaterialCategory[category],
      cost: data.cost,
      count: data.count,
    }));
    
    const recentLogs = logs
      .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
      .slice(0, 5);
    
    return {
      totalCost,
      totalItems,
      costByCategory,
      recentLogs,
    };
  },

  // Search material logs
  search: async (siteId: string, query: string): Promise<MaterialLog[]> => {
    const logs = await materialsService.getAll(siteId);
    const searchTerm = query.toLowerCase();
    
    return logs.filter(log => 
      log.materialName.toLowerCase().includes(searchTerm) ||
      log.description?.toLowerCase().includes(searchTerm) ||
      log.supplier?.toLowerCase().includes(searchTerm) ||
      log.notes?.toLowerCase().includes(searchTerm)
    );
  },

  // Filter by date range
  filterByDateRange: async (siteId: string, startDate: string, endDate: string): Promise<MaterialLog[]> => {
    const logs = await materialsService.getAll(siteId);
    const start = new Date(startDate);
    const end = new Date(endDate);
    
    return logs.filter(log => {
      const logDate = new Date(log.logDate);
      return logDate >= start && logDate <= end;
    });
  },

  // Filter by category
  filterByCategory: async (siteId: string, category: MaterialCategory): Promise<MaterialLog[]> => {
    return materialsService.getAll(siteId, { category });
  },

  // Filter by log type
  filterByLogType: async (siteId: string, logType: MaterialLogType): Promise<MaterialLog[]> => {
    const logs = await materialsService.getAll(siteId);
    return logs.filter(log => log.logType === logType);
  },

  // Get monthly spending for a site
  getMonthlySpending: async (siteId: string, months: number = 12): Promise<Array<{
    month: string;
    cost: number;
    count: number;
  }>> => {
    const logs = await materialsService.getAll(siteId);
    const monthlyData = new Map<string, { cost: number; count: number }>();
    
    const startDate = new Date();
    startDate.setMonth(startDate.getMonth() - months);
    
    logs
      .filter(log => new Date(log.logDate) >= startDate)
      .forEach(log => {
        const date = new Date(log.logDate);
        const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
        
        const existing = monthlyData.get(monthKey) || { cost: 0, count: 0 };
        monthlyData.set(monthKey, {
          cost: existing.cost + log.totalCost,
          count: existing.count + 1,
        });
      });
    
    return Array.from(monthlyData.entries())
      .map(([month, data]) => ({ month, ...data }))
      .sort((a, b) => a.month.localeCompare(b.month));
  },
};

export default materialsService;
