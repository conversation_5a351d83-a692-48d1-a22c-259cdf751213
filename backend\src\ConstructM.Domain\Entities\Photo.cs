using System.ComponentModel.DataAnnotations;
using ConstructM.Domain.Common;

namespace ConstructM.Domain.Entities;

public class Photo : BaseEntity
{
    [Required]
    [MaxLength(200)]
    public string Title { get; set; } = string.Empty;
    
    [MaxLength(1000)]
    public string? Description { get; set; }
    
    [Required]
    [MaxLength(500)]
    public string FilePath { get; set; } = string.Empty;
    
    [Required]
    [MaxLength(100)]
    public string FileName { get; set; } = string.Empty;
    
    [Required]
    [MaxLength(50)]
    public string ContentType { get; set; } = string.Empty;
    
    public long FileSize { get; set; }
    
    public int? Width { get; set; }
    
    public int? Height { get; set; }
    
    [Required]
    public PhotoCategory Category { get; set; } = PhotoCategory.Progress;
    
    public DateTime PhotoTakenAt { get; set; } = DateTime.UtcNow;
    
    [MaxLength(200)]
    public string? Location { get; set; }
    
    public decimal? Latitude { get; set; }
    
    public decimal? Longitude { get; set; }
    
    [MaxLength(1000)]
    public string? Tags { get; set; } // Comma-separated tags
    
    public bool IsPublic { get; set; } = false;
    
    public bool IsFeatured { get; set; } = false;
    
    [MaxLength(500)]
    public string? ThumbnailPath { get; set; }
    
    // Navigation properties
    public Guid SiteId { get; set; }
    public virtual Site Site { get; set; } = null!;
    
    public Guid UploadedByUserId { get; set; }
    public virtual User UploadedByUser { get; set; } = null!;
    
    // Computed properties
    public string[] TagList => string.IsNullOrEmpty(Tags) 
        ? Array.Empty<string>() 
        : Tags.Split(',', StringSplitOptions.RemoveEmptyEntries)
               .Select(t => t.Trim())
               .ToArray();
               
    public string FileSizeFormatted
    {
        get
        {
            if (FileSize < 1024) return $"{FileSize} B";
            if (FileSize < 1024 * 1024) return $"{FileSize / 1024:F1} KB";
            if (FileSize < 1024 * 1024 * 1024) return $"{FileSize / (1024 * 1024):F1} MB";
            return $"{FileSize / (1024 * 1024 * 1024):F1} GB";
        }
    }
}

public enum PhotoCategory
{
    Progress = 0,
    BeforeAfter = 1,
    Materials = 2,
    Equipment = 3,
    Safety = 4,
    Quality = 5,
    Documentation = 6,
    Other = 7
}
