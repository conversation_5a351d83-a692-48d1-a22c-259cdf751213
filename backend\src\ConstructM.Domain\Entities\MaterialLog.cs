using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using ConstructM.Domain.Common;

namespace ConstructM.Domain.Entities;

public class MaterialLog : BaseEntity
{
    [Required]
    [MaxLength(200)]
    public string MaterialName { get; set; } = string.Empty;
    
    [MaxLength(500)]
    public string? Description { get; set; }
    
    [Required]
    public MaterialCategory Category { get; set; }
    
    [Required]
    [Column(TypeName = "decimal(18,4)")]
    public decimal Quantity { get; set; }
    
    [Required]
    [MaxLength(20)]
    public string Unit { get; set; } = string.Empty;
    
    [Required]
    [Column(TypeName = "decimal(18,2)")]
    public decimal UnitRate { get; set; }
    
    [Column(TypeName = "decimal(18,2)")]
    public decimal TotalCost { get; set; }
    
    [Required]
    public DateTime LogDate { get; set; } = DateTime.UtcNow;
    
    [Required]
    public MaterialLogType LogType { get; set; } = MaterialLogType.Usage;
    
    [MaxLength(200)]
    public string? Supplier { get; set; }
    
    [MaxLength(100)]
    public string? InvoiceNumber { get; set; }
    
    [MaxLength(1000)]
    public string? Notes { get; set; }
    
    [MaxLength(500)]
    public string? ReceiptUrl { get; set; }
    
    // Navigation properties
    public Guid SiteId { get; set; }
    public virtual Site Site { get; set; } = null!;
    
    public Guid LoggedByUserId { get; set; }
    public virtual User LoggedByUser { get; set; } = null!;
}

public enum MaterialCategory
{
    Concrete = 0,
    Steel = 1,
    Wood = 2,
    Brick = 3,
    Tile = 4,
    Paint = 5,
    Electrical = 6,
    Plumbing = 7,
    Roofing = 8,
    Insulation = 9,
    Hardware = 10,
    Tools = 11,
    Equipment = 12,
    Other = 13
}

public enum MaterialLogType
{
    Purchase = 0,
    Usage = 1,
    Return = 2,
    Waste = 3,
    Transfer = 4
}
