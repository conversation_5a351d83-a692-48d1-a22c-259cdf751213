import React from 'react';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>Axis,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
} from 'recharts';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/Card';

interface MaterialData {
  material: string;
  quantity: number;
  unit: string;
  cost: number;
}

interface MaterialUsageChartProps {
  data: MaterialData[];
}

export const MaterialUsageChart: React.FC<MaterialUsageChartProps> = ({ data }) => {
  const formatCurrency = (value: number): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-white p-4 border border-gray-200 rounded-lg shadow-lg">
          <p className="font-medium text-gray-900 mb-2">{label}</p>
          <p className="text-sm text-blue-600">
            Cost: {formatCurrency(payload[0].value)}
          </p>
          <p className="text-sm text-gray-600">
            Quantity: {data.quantity.toLocaleString()} {data.unit}
          </p>
          <p className="text-sm text-gray-600">
            Unit Cost: {formatCurrency(data.cost / data.quantity)}/{data.unit}
          </p>
        </div>
      );
    }
    return null;
  };

  // Calculate totals
  const totalCost = data.reduce((sum, item) => sum + item.cost, 0);
  const mostExpensive = data.reduce((prev, current) => 
    prev.cost > current.cost ? prev : current
  );

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          Material Usage by Cost
          <div className="text-right">
            <div className="text-sm text-gray-500">Total Cost</div>
            <div className="text-lg font-bold text-gray-900">
              {formatCurrency(totalCost)}
            </div>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="h-80 mb-6">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
              <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
              <XAxis 
                dataKey="material" 
                stroke="#6b7280"
                fontSize={12}
                angle={-45}
                textAnchor="end"
                height={80}
              />
              <YAxis 
                stroke="#6b7280"
                fontSize={12}
                tickFormatter={(value) => `$${(value / 1000).toFixed(0)}k`}
              />
              <Tooltip content={<CustomTooltip />} />
              <Bar 
                dataKey="cost" 
                fill="#8b5cf6"
                radius={[4, 4, 0, 0]}
              />
            </BarChart>
          </ResponsiveContainer>
        </div>

        {/* Material Statistics */}
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 mb-6">
          <div className="bg-purple-50 p-4 rounded-lg">
            <div className="text-sm font-medium text-purple-700">Most Expensive Material</div>
            <div className="text-lg font-bold text-purple-900">{mostExpensive.material}</div>
            <div className="text-sm text-purple-600">
              {formatCurrency(mostExpensive.cost)} ({((mostExpensive.cost / totalCost) * 100).toFixed(1)}% of total)
            </div>
          </div>
          <div className="bg-blue-50 p-4 rounded-lg">
            <div className="text-sm font-medium text-blue-700">Material Types</div>
            <div className="text-lg font-bold text-blue-900">{data.length}</div>
            <div className="text-sm text-blue-600">
              Different materials used
            </div>
          </div>
        </div>

        {/* Detailed Material List */}
        <div>
          <h4 className="text-sm font-medium text-gray-900 mb-3">Material Details</h4>
          <div className="space-y-3">
            {data
              .sort((a, b) => b.cost - a.cost)
              .map((item, index) => {
                const percentage = ((item.cost / totalCost) * 100).toFixed(1);
                const unitCost = item.cost / item.quantity;
                
                return (
                  <div key={index} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                    <div className="flex-1">
                      <div className="flex items-center justify-between mb-2">
                        <span className="font-medium text-gray-900">{item.material}</span>
                        <span className="text-sm font-medium text-gray-900">
                          {formatCurrency(item.cost)}
                        </span>
                      </div>
                      <div className="flex items-center justify-between text-sm text-gray-600">
                        <span>
                          {item.quantity.toLocaleString()} {item.unit} × {formatCurrency(unitCost)}/{item.unit}
                        </span>
                        <span>{percentage}% of total</span>
                      </div>
                      <div className="mt-2">
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div
                            className="bg-purple-600 h-2 rounded-full transition-all duration-300"
                            style={{ width: `${percentage}%` }}
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })}
          </div>
        </div>

        {/* Cost Distribution */}
        <div className="mt-6 p-4 bg-gray-50 rounded-lg">
          <h4 className="text-sm font-medium text-gray-900 mb-3">Cost Distribution Analysis</h4>
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-3">
            <div>
              <div className="text-xs text-gray-500">Average Cost per Material</div>
              <div className="text-lg font-bold text-gray-900">
                {formatCurrency(totalCost / data.length)}
              </div>
            </div>
            <div>
              <div className="text-xs text-gray-500">Highest Unit Cost</div>
              <div className="text-lg font-bold text-gray-900">
                {formatCurrency(Math.max(...data.map(item => item.cost / item.quantity)))}
              </div>
            </div>
            <div>
              <div className="text-xs text-gray-500">Total Quantity Items</div>
              <div className="text-lg font-bold text-gray-900">
                {data.reduce((sum, item) => sum + item.quantity, 0).toLocaleString()}
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
