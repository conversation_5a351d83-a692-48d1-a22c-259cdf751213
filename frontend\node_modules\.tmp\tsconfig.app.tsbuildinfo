{"root": ["../../src/app.tsx", "../../src/main.tsx", "../../src/vite-env.d.ts", "../../src/components/auth/protectedroute.tsx", "../../src/components/common/errorboundary.tsx", "../../src/components/layout/header.tsx", "../../src/components/layout/layout.tsx", "../../src/components/layout/sidebar.tsx", "../../src/components/ui/loadingspinner.tsx", "../../src/components/ui/toaster.tsx", "../../src/context/authcontext.tsx", "../../src/hooks/usematerials.ts", "../../src/hooks/usesites.ts", "../../src/pages/dashboardpage.tsx", "../../src/pages/auth/loginpage.tsx", "../../src/pages/auth/registerpage.tsx", "../../src/pages/materials/materialspage.tsx", "../../src/pages/photos/photospage.tsx", "../../src/pages/reports/reportspage.tsx", "../../src/pages/sites/createsitepage.tsx", "../../src/pages/sites/editsitepage.tsx", "../../src/pages/sites/sitedetailspage.tsx", "../../src/pages/sites/sitespage.tsx", "../../src/services/api.ts", "../../src/services/auth.ts", "../../src/services/materials.ts", "../../src/services/photos.ts", "../../src/services/reports.ts", "../../src/services/sites.ts", "../../src/types/index.ts", "../../src/utils/cn.ts"], "errors": true, "version": "5.8.3"}