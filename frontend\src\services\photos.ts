import { apiClient, uploadFile } from './api';
import type { Photo, PhotoCategory } from '@/types';

export interface UploadPhotoRequest {
  title?: string;
  description?: string;
  category: PhotoCategory;
  photoTakenAt?: string;
  location?: string;
  latitude?: number;
  longitude?: number;
  tags?: string;
  isPublic?: boolean;
}

export const photosService = {
  // Get all photos for a site
  getAll: async (siteId: string, category?: PhotoCategory): Promise<Photo[]> => {
    const params = new URLSearchParams();
    if (category !== undefined) params.append('category', category.toString());
    
    const url = `/sites/${siteId}/photos${params.toString() ? `?${params.toString()}` : ''}`;
    const response = await apiClient.get<Photo[]>(url);
    return response.data;
  },

  // Get a specific photo
  getById: async (siteId: string, id: string): Promise<Photo> => {
    const response = await apiClient.get<Photo>(`/sites/${siteId}/photos/${id}`);
    return response.data;
  },

  // Upload a new photo
  upload: async (
    siteId: string,
    file: File,
    data: UploadPhotoRequest,
    onProgress?: (progress: number) => void
  ): Promise<{ id: string; message: string }> => {
    const response = await uploadFile(
      `/sites/${siteId}/photos`,
      file,
      data,
      onProgress
    );
    return response.data;
  },

  // Delete a photo
  delete: async (siteId: string, id: string): Promise<{ message: string }> => {
    const response = await apiClient.delete<{ message: string }>(`/sites/${siteId}/photos/${id}`);
    return response.data;
  },

  // Get photo statistics for a site
  getStatistics: async (siteId: string): Promise<{
    totalPhotos: number;
    photosByCategory: Array<{ category: string; count: number }>;
    recentPhotos: Photo[];
    totalSize: number;
  }> => {
    const photos = await photosService.getAll(siteId);
    
    const totalPhotos = photos.length;
    const totalSize = photos.reduce((sum, photo) => sum + photo.fileSize, 0);
    
    // Group by category
    const categoryMap = new Map<PhotoCategory, number>();
    photos.forEach(photo => {
      const count = categoryMap.get(photo.category) || 0;
      categoryMap.set(photo.category, count + 1);
    });
    
    const photosByCategory = Array.from(categoryMap.entries()).map(([category, count]) => ({
      category: PhotoCategory[category],
      count,
    }));
    
    const recentPhotos = photos
      .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
      .slice(0, 6);
    
    return {
      totalPhotos,
      photosByCategory,
      recentPhotos,
      totalSize,
    };
  },

  // Search photos
  search: async (siteId: string, query: string): Promise<Photo[]> => {
    const photos = await photosService.getAll(siteId);
    const searchTerm = query.toLowerCase();
    
    return photos.filter(photo => 
      photo.title.toLowerCase().includes(searchTerm) ||
      photo.description?.toLowerCase().includes(searchTerm) ||
      photo.location?.toLowerCase().includes(searchTerm) ||
      photo.tags?.toLowerCase().includes(searchTerm)
    );
  },

  // Filter by category
  filterByCategory: async (siteId: string, category: PhotoCategory): Promise<Photo[]> => {
    return photosService.getAll(siteId, category);
  },

  // Filter by date range
  filterByDateRange: async (siteId: string, startDate: string, endDate: string): Promise<Photo[]> => {
    const photos = await photosService.getAll(siteId);
    const start = new Date(startDate);
    const end = new Date(endDate);
    
    return photos.filter(photo => {
      const photoDate = new Date(photo.photoTakenAt);
      return photoDate >= start && photoDate <= end;
    });
  },

  // Filter by tags
  filterByTags: async (siteId: string, tags: string[]): Promise<Photo[]> => {
    const photos = await photosService.getAll(siteId);
    
    return photos.filter(photo => {
      if (!photo.tags) return false;
      const photoTags = photo.tagList.map(tag => tag.toLowerCase());
      return tags.some(tag => photoTags.includes(tag.toLowerCase()));
    });
  },

  // Get featured photos
  getFeatured: async (siteId: string): Promise<Photo[]> => {
    const photos = await photosService.getAll(siteId);
    return photos.filter(photo => photo.isFeatured);
  },

  // Get public photos
  getPublic: async (siteId: string): Promise<Photo[]> => {
    const photos = await photosService.getAll(siteId);
    return photos.filter(photo => photo.isPublic);
  },

  // Get photo URL for display
  getPhotoUrl: (photo: Photo): string => {
    const baseUrl = import.meta.env.VITE_API_URL?.replace('/api', '') || 'http://localhost:5000';
    return `${baseUrl}/${photo.filePath}`;
  },

  // Get thumbnail URL
  getThumbnailUrl: (photo: Photo): string => {
    if (photo.thumbnailPath) {
      const baseUrl = import.meta.env.VITE_API_URL?.replace('/api', '') || 'http://localhost:5000';
      return `${baseUrl}/${photo.thumbnailPath}`;
    }
    return photosService.getPhotoUrl(photo);
  },

  // Validate file before upload
  validateFile: (file: File): { isValid: boolean; error?: string } => {
    const maxSize = parseInt(import.meta.env.VITE_FILE_UPLOAD_MAX_SIZE || '10485760'); // 10MB
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
    
    if (!allowedTypes.includes(file.type)) {
      return {
        isValid: false,
        error: 'Invalid file type. Only JPEG, PNG, and GIF files are allowed.',
      };
    }
    
    if (file.size > maxSize) {
      return {
        isValid: false,
        error: `File size exceeds ${Math.round(maxSize / 1024 / 1024)}MB limit.`,
      };
    }
    
    return { isValid: true };
  },
};

export default photosService;
