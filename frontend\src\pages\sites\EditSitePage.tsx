import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, useNavigate, Link } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useSite, useUpdateSite } from '@/hooks/useSites';
import { useToast } from '@/components/ui/Toaster';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/Card';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { SiteType, SiteStatus } from '@/types';
import type { UpdateSiteRequest } from '@/types';

const updateSiteSchema = z.object({
  name: z.string().min(1, 'Site name is required'),
  description: z.string().optional(),
  location: z.string().min(1, 'Location is required'),
  address: z.string().optional(),
  city: z.string().optional(),
  state: z.string().optional(),
  postalCode: z.string().optional(),
  area: z.number().positive('Area must be positive').optional(),
  areaUnit: z.string().optional(),
  type: z.number(),
  status: z.number(),
  startDate: z.string().optional(),
  expectedEndDate: z.string().optional(),
  actualEndDate: z.string().optional(),
  estimatedBudget: z.number().positive('Budget must be positive').optional(),
  notes: z.string().optional(),
});

type UpdateSiteFormData = z.infer<typeof updateSiteSchema>;

export const EditSitePage: React.FC = () => {
  const { id: siteId } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { addToast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);

  // API hooks
  const { data: site, isLoading: siteLoading, error } = useSite(siteId!);
  const updateSiteMutation = useUpdateSite();

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    watch,
  } = useForm<UpdateSiteFormData>({
    resolver: zodResolver(updateSiteSchema),
  });

  // Reset form when site data is loaded
  useEffect(() => {
    if (site) {
      reset({
        name: site.name,
        description: site.description || '',
        location: site.location,
        address: site.address || '',
        city: site.city || '',
        state: site.state || '',
        postalCode: site.postalCode || '',
        area: site.area || undefined,
        areaUnit: site.areaUnit || 'sqft',
        type: site.type,
        status: site.status,
        startDate: site.startDate ? site.startDate.split('T')[0] : '',
        expectedEndDate: site.expectedEndDate ? site.expectedEndDate.split('T')[0] : '',
        actualEndDate: site.actualEndDate ? site.actualEndDate.split('T')[0] : '',
        estimatedBudget: site.estimatedBudget || undefined,
        notes: site.notes || '',
      });
    }
  }, [site, reset]);

  const onSubmit = async (data: UpdateSiteFormData) => {
    try {
      setIsSubmitting(true);

      const updateData: UpdateSiteRequest = {
        ...data,
        area: data.area || undefined,
        estimatedBudget: data.estimatedBudget || undefined,
        startDate: data.startDate || undefined,
        expectedEndDate: data.expectedEndDate || undefined,
        actualEndDate: data.actualEndDate || undefined,
      };

      await updateSiteMutation.mutateAsync({ id: siteId!, data: updateData });

      addToast({
        type: 'success',
        title: 'Site Updated',
        message: 'Construction site has been updated successfully.',
      });

      navigate(`/sites/${siteId}`);
    } catch (error: any) {
      addToast({
        type: 'error',
        title: 'Error',
        message: error.response?.data?.message || 'Failed to update site.',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const getSiteTypeLabel = (type: number): string => {
    const labels = {
      [SiteType.Residential]: 'Residential',
      [SiteType.Commercial]: 'Commercial',
      [SiteType.Industrial]: 'Industrial',
      [SiteType.Infrastructure]: 'Infrastructure',
      [SiteType.Renovation]: 'Renovation',
      [SiteType.Other]: 'Other',
    };
    return labels[type] || 'Unknown';
  };

  const getSiteStatusLabel = (status: number): string => {
    const labels = {
      [SiteStatus.Planning]: 'Planning',
      [SiteStatus.InProgress]: 'In Progress',
      [SiteStatus.OnHold]: 'On Hold',
      [SiteStatus.Completed]: 'Completed',
      [SiteStatus.Cancelled]: 'Cancelled',
    };
    return labels[status] || 'Unknown';
  };

  if (siteLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (error || !site) {
    return (
      <div className="text-center py-12">
        <div className="text-red-600 mb-4">
          <svg className="mx-auto h-12 w-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
          </svg>
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">Site not found</h3>
        <p className="text-gray-500 mb-4">The site you're trying to edit doesn't exist or you don't have access to it.</p>
        <Link to="/sites" className="text-blue-600 hover:text-blue-500">
          Back to Sites
        </Link>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="md:flex md:items-center md:justify-between">
        <div className="flex-1 min-w-0">
          <div className="flex items-center">
            <Link
              to={`/sites/${siteId}`}
              className="mr-4 text-gray-400 hover:text-gray-600"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </Link>
            <div>
              <h2 className="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
                Edit Site - {site.name}
              </h2>
              <p className="mt-1 text-sm text-gray-500">
                Update the details and settings for this construction site.
              </p>
            </div>
          </div>
        </div>
        <div className="mt-4 flex md:mt-0 md:ml-4">
          <Button
            variant="outline"
            onClick={() => navigate(`/sites/${siteId}`)}
          >
            Cancel
          </Button>
        </div>
      </div>

      {/* Form */}
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Basic Information</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
              <div className="sm:col-span-2">
                <Input
                  label="Site Name *"
                  {...register('name')}
                  error={errors.name?.message}
                  placeholder="Enter site name"
                />
              </div>

              <div className="sm:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Description
                </label>
                <textarea
                  {...register('description')}
                  rows={3}
                  className="block w-full rounded-lg border border-gray-300 bg-white px-3 py-2 text-sm placeholder-gray-500 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                  placeholder="Enter site description"
                />
                {errors.description && (
                  <p className="mt-1 text-sm text-red-600">{errors.description.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Site Type *
                </label>
                <select
                  {...register('type', { valueAsNumber: true })}
                  className="block w-full rounded-lg border border-gray-300 bg-white px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                >
                  {Object.entries(SiteType).map(([key, value]) => (
                    <option key={key} value={value}>
                      {getSiteTypeLabel(value)}
                    </option>
                  ))}
                </select>
                {errors.type && (
                  <p className="mt-1 text-sm text-red-600">{errors.type.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Status *
                </label>
                <select
                  {...register('status', { valueAsNumber: true })}
                  className="block w-full rounded-lg border border-gray-300 bg-white px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                >
                  {Object.entries(SiteStatus).map(([key, value]) => (
                    <option key={key} value={value}>
                      {getSiteStatusLabel(value)}
                    </option>
                  ))}
                </select>
                {errors.status && (
                  <p className="mt-1 text-sm text-red-600">{errors.status.message}</p>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Location Details</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
              <div className="sm:col-span-2">
                <Input
                  label="Location *"
                  {...register('location')}
                  error={errors.location?.message}
                  placeholder="Enter location"
                />
              </div>

              <div className="sm:col-span-2">
                <Input
                  label="Address"
                  {...register('address')}
                  placeholder="Enter full address"
                />
              </div>

              <div>
                <Input
                  label="City"
                  {...register('city')}
                  placeholder="Enter city"
                />
              </div>

              <div>
                <Input
                  label="State/Province"
                  {...register('state')}
                  placeholder="Enter state/province"
                />
              </div>

              <div>
                <Input
                  label="Postal Code"
                  {...register('postalCode')}
                  placeholder="Enter postal code"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Area
                </label>
                <div className="flex rounded-lg shadow-sm">
                  <input
                    {...register('area', { valueAsNumber: true })}
                    type="number"
                    step="0.01"
                    className="block w-full rounded-l-lg border border-gray-300 bg-white px-3 py-2 text-sm placeholder-gray-500 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                    placeholder="0.00"
                  />
                  <select
                    {...register('areaUnit')}
                    className="block w-24 rounded-r-lg border border-l-0 border-gray-300 bg-white px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                  >
                    <option value="sqft">sq ft</option>
                    <option value="sqm">sq m</option>
                    <option value="acres">acres</option>
                  </select>
                </div>
                {errors.area && (
                  <p className="mt-1 text-sm text-red-600">{errors.area.message}</p>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Project Timeline</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 gap-6 sm:grid-cols-3">
              <Input
                label="Start Date"
                type="date"
                {...register('startDate')}
              />

              <Input
                label="Expected End Date"
                type="date"
                {...register('expectedEndDate')}
              />

              <Input
                label="Actual End Date"
                type="date"
                {...register('actualEndDate')}
                helperText="Only set if project is completed"
              />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Budget Information</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Estimated Budget
                </label>
                <div className="relative rounded-lg shadow-sm">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <span className="text-gray-500 sm:text-sm">$</span>
                  </div>
                  <input
                    {...register('estimatedBudget', { valueAsNumber: true })}
                    type="number"
                    step="0.01"
                    className="block w-full pl-7 rounded-lg border border-gray-300 bg-white px-3 py-2 text-sm placeholder-gray-500 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                    placeholder="0.00"
                  />
                </div>
                {errors.estimatedBudget && (
                  <p className="mt-1 text-sm text-red-600">{errors.estimatedBudget.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Current Actual Cost
                </label>
                <div className="relative rounded-lg shadow-sm">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <span className="text-gray-500 sm:text-sm">$</span>
                  </div>
                  <input
                    type="number"
                    value={site.actualCost || 0}
                    disabled
                    className="block w-full pl-7 rounded-lg border border-gray-300 bg-gray-50 px-3 py-2 text-sm text-gray-500"
                  />
                </div>
                <p className="mt-1 text-xs text-gray-500">
                  Calculated from material logs and expenses
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Additional Notes</CardTitle>
          </CardHeader>
          <CardContent>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Notes
            </label>
            <textarea
              {...register('notes')}
              rows={4}
              className="block w-full rounded-lg border border-gray-300 bg-white px-3 py-2 text-sm placeholder-gray-500 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
              placeholder="Enter any additional notes or comments"
            />
          </CardContent>
        </Card>

        {/* Submit Buttons */}
        <div className="flex justify-end space-x-3">
          <Button
            type="button"
            variant="outline"
            onClick={() => navigate(`/sites/${siteId}`)}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            loading={isSubmitting}
          >
            Update Site
          </Button>
        </div>
      </form>
    </div>
  );
};
