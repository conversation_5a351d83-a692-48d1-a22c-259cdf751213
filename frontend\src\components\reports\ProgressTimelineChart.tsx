import React from 'react';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>Axis,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  ReferenceLine,
} from 'recharts';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';

interface ProgressData {
  date: string;
  milestone: string;
  progress: number;
}

interface ProgressTimelineChartProps {
  data: ProgressData[];
}

export const ProgressTimelineChart: React.FC<ProgressTimelineChartProps> = ({ data }) => {
  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
    });
  };

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const dataPoint = payload[0].payload;
      return (
        <div className="bg-white p-4 border border-gray-200 rounded-lg shadow-lg">
          <p className="font-medium text-gray-900 mb-2">{formatDate(label)}</p>
          <p className="text-sm text-blue-600 mb-1">
            Progress: {payload[0].value}%
          </p>
          <p className="text-sm text-gray-600">
            Milestone: {dataPoint.milestone}
          </p>
        </div>
      );
    }
    return null;
  };

  // Calculate progress metrics
  const currentProgress = data[data.length - 1]?.progress || 0;
  const startDate = new Date(data[0]?.date);
  const endDate = new Date(data[data.length - 1]?.date);
  const totalDays = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
  const progressRate = currentProgress / totalDays;

  // Estimate completion date based on current rate
  const daysToCompletion = Math.ceil((100 - currentProgress) / progressRate);
  const estimatedCompletion = new Date();
  estimatedCompletion.setDate(estimatedCompletion.getDate() + daysToCompletion);

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          Project Progress Timeline
          <div className="text-right">
            <div className="text-sm text-gray-500">Current Progress</div>
            <div className="text-2xl font-bold text-blue-600">{currentProgress}%</div>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="h-80 mb-6">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
              <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
              <XAxis 
                dataKey="date" 
                stroke="#6b7280"
                fontSize={12}
                tickFormatter={formatDate}
              />
              <YAxis 
                stroke="#6b7280"
                fontSize={12}
                domain={[0, 100]}
                tickFormatter={(value) => `${value}%`}
              />
              <Tooltip content={<CustomTooltip />} />
              <ReferenceLine y={100} stroke="#ef4444" strokeDasharray="5 5" />
              <Line
                type="monotone"
                dataKey="progress"
                stroke="#3b82f6"
                strokeWidth={3}
                dot={{ fill: '#3b82f6', strokeWidth: 2, r: 6 }}
                activeDot={{ r: 8, stroke: '#3b82f6', strokeWidth: 2 }}
              />
            </LineChart>
          </ResponsiveContainer>
        </div>

        {/* Progress Statistics */}
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-4 mb-6">
          <div className="bg-blue-50 p-4 rounded-lg">
            <div className="text-sm font-medium text-blue-700">Progress Rate</div>
            <div className="text-lg font-bold text-blue-900">
              {progressRate.toFixed(1)}%/day
            </div>
          </div>
          <div className="bg-green-50 p-4 rounded-lg">
            <div className="text-sm font-medium text-green-700">Days Active</div>
            <div className="text-lg font-bold text-green-900">{totalDays}</div>
          </div>
          <div className="bg-yellow-50 p-4 rounded-lg">
            <div className="text-sm font-medium text-yellow-700">Est. Completion</div>
            <div className="text-lg font-bold text-yellow-900">
              {estimatedCompletion.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}
            </div>
          </div>
          <div className="bg-purple-50 p-4 rounded-lg">
            <div className="text-sm font-medium text-purple-700">Milestones</div>
            <div className="text-lg font-bold text-purple-900">{data.length}</div>
          </div>
        </div>

        {/* Milestone Timeline */}
        <div>
          <h4 className="text-sm font-medium text-gray-900 mb-4">Milestone Timeline</h4>
          <div className="space-y-4">
            {data.map((item, index) => {
              const isCompleted = item.progress === 100;
              const isCurrent = index === data.length - 1;
              
              return (
                <div key={index} className="relative">
                  {index !== data.length - 1 && (
                    <div className="absolute left-4 top-8 w-0.5 h-12 bg-gray-200" />
                  )}
                  <div className="flex items-start space-x-4">
                    <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center ${
                      isCompleted 
                        ? 'bg-green-500 text-white' 
                        : isCurrent 
                        ? 'bg-blue-500 text-white' 
                        : 'bg-gray-200 text-gray-600'
                    }`}>
                      {isCompleted ? (
                        <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                      ) : (
                        <span className="text-xs font-bold">{index + 1}</span>
                      )}
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-gray-900">{item.milestone}</p>
                          <p className="text-xs text-gray-500">{formatDate(item.date)}</p>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Badge 
                            variant={isCompleted ? 'success' : isCurrent ? 'info' : 'default'}
                            size="sm"
                          >
                            {item.progress}%
                          </Badge>
                          {isCurrent && (
                            <Badge variant="warning" size="sm">
                              Current
                            </Badge>
                          )}
                        </div>
                      </div>
                      <div className="mt-2">
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div
                            className={`h-2 rounded-full transition-all duration-300 ${
                              isCompleted 
                                ? 'bg-green-500' 
                                : isCurrent 
                                ? 'bg-blue-500' 
                                : 'bg-gray-400'
                            }`}
                            style={{ width: `${item.progress}%` }}
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Progress Insights */}
        <div className="mt-6 p-4 bg-gray-50 rounded-lg">
          <h4 className="text-sm font-medium text-gray-900 mb-3">Progress Insights</h4>
          <div className="space-y-2 text-sm text-gray-600">
            <p>
              • Project has been active for <strong>{totalDays} days</strong> with an average progress rate of <strong>{progressRate.toFixed(1)}% per day</strong>
            </p>
            <p>
              • At the current rate, the project is estimated to complete on <strong>{estimatedCompletion.toLocaleDateString()}</strong>
            </p>
            <p>
              • <strong>{data.length} milestones</strong> have been tracked, with the most recent being "{data[data.length - 1]?.milestone}"
            </p>
            {currentProgress >= 75 && (
              <p className="text-green-600">
                • Project is in the final phase with <strong>{100 - currentProgress}%</strong> remaining
              </p>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
