# Database Setup

This directory contains database initialization scripts and documentation for the ConstructM application.

## Quick Start with Docker

1. Start the PostgreSQL container:
```bash
docker-compose -f docker-compose.dev.yml up postgres-dev -d
```

2. The database will be automatically created with the name `constructm_dev`

3. Access pgAdmin at http://localhost:8080
   - Email: <EMAIL>
   - Password: admin123

## Manual Setup

If you prefer to set up PostgreSQL manually:

1. Install PostgreSQL 14+
2. Create a database named `constructm_dev`
3. Create a user `constructm_user` with password `constructm_password`
4. Grant all privileges on the database to the user

```sql
CREATE DATABASE constructm_dev;
CREATE USER constructm_user WITH PASSWORD 'constructm_password';
GRANT ALL PRIVILEGES ON DATABASE constructm_dev TO constructm_user;
```

## Connection String

For development:
```
Host=localhost;Database=constructm_dev;Username=constructm_user;Password=constructm_password
```

## Entity Framework Migrations

The backend uses Entity Framework Core for database migrations. After setting up the database:

1. Navigate to the backend directory
2. Run migrations:
```bash
cd backend
dotnet ef database update --project src/ConstructM.Infrastructure --startup-project src/ConstructM.API
```

## Database Schema

The application uses the following main entities:

- **Users**: User accounts and authentication
- **Firms**: Construction companies/organizations  
- **Sites**: Construction site information
- **Estimates**: Cost and material estimates
- **MaterialLogs**: Material usage tracking
- **Photos**: Progress photo documentation

See the Entity Framework models in `backend/src/ConstructM.Domain/Entities/` for detailed schema information.
