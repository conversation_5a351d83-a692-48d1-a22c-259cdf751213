import React, { useState, useCallback } from 'react';
import { use<PERSON>ara<PERSON>, Link } from 'react-router-dom';
import { useDropzone } from 'react-dropzone';
import { Upload, Image, Filter, Grid, List, Download, Eye, Trash2 } from 'lucide-react';
import { useSite } from '@/hooks/useSites';
import { useToast } from '@/components/ui/Toaster';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';
import { EmptyState } from '@/components/ui/EmptyState';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { PhotoCategory } from '@/types';
// import { PhotoUploadModal } from '@/components/photos/PhotoUploadModal';
// import { PhotoGallery } from '@/components/photos/PhotoGallery';
// import { PhotoStatsCards } from '@/components/photos/PhotoStatsCards';

export const PhotosPage: React.FC = () => {
  const { id: siteId } = useParams<{ id: string }>();
  const { addToast } = useToast();

  // State for filters and modals
  const [searchQuery, setSearchQuery] = useState('');
  const [categoryFilter, setCategoryFilter] = useState<PhotoCategory | 'all'>('all');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [isUploadModalOpen, setIsUploadModalOpen] = useState(false);
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);

  // API hooks
  const { data: site, isLoading: siteLoading } = useSite(siteId!);

  // Mock data for photos (replace with actual API calls)
  const photos = [
    {
      id: '1',
      title: 'Foundation Progress',
      description: 'Foundation work completed',
      filePath: '/uploads/photos/foundation.jpg',
      fileName: 'foundation.jpg',
      contentType: 'image/jpeg',
      fileSize: 2048000,
      category: PhotoCategory.Progress,
      photoTakenAt: '2024-01-15T10:00:00Z',
      location: 'Main Building',
      tags: 'foundation,concrete,progress',
      isPublic: true,
      isFeatured: false,
      createdAt: '2024-01-15T10:30:00Z',
      uploadedBy: 'John Doe',
      fileSizeFormatted: '2.0 MB',
      tagList: ['foundation', 'concrete', 'progress'],
    },
    {
      id: '2',
      title: 'Steel Frame Installation',
      description: 'Steel frame structure in progress',
      filePath: '/uploads/photos/steel-frame.jpg',
      fileName: 'steel-frame.jpg',
      contentType: 'image/jpeg',
      fileSize: 3072000,
      category: PhotoCategory.Progress,
      photoTakenAt: '2024-01-20T14:00:00Z',
      location: 'Building A',
      tags: 'steel,frame,structure',
      isPublic: true,
      isFeatured: true,
      createdAt: '2024-01-20T14:30:00Z',
      uploadedBy: 'Jane Smith',
      fileSizeFormatted: '3.0 MB',
      tagList: ['steel', 'frame', 'structure'],
    },
  ];

  const isLoading = siteLoading;

  // Helper functions
  const getCategoryLabel = (category: PhotoCategory): string => {
    const labels = {
      [PhotoCategory.Progress]: 'Progress',
      [PhotoCategory.BeforeAfter]: 'Before/After',
      [PhotoCategory.Materials]: 'Materials',
      [PhotoCategory.Equipment]: 'Equipment',
      [PhotoCategory.Safety]: 'Safety',
      [PhotoCategory.Quality]: 'Quality',
      [PhotoCategory.Documentation]: 'Documentation',
      [PhotoCategory.Other]: 'Other',
    };
    return labels[category] || 'Unknown';
  };

  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  // Drag and drop handlers
  const onDrop = useCallback((acceptedFiles: File[]) => {
    setSelectedFiles(acceptedFiles);
    setIsUploadModalOpen(true);
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/*': ['.jpeg', '.jpg', '.png', '.gif'],
    },
    multiple: true,
  });

  // Filter photos based on search and filters
  const filteredPhotos = photos.filter((photo) => {
    const matchesSearch = searchQuery === '' ||
      photo.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      photo.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      photo.location?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      photo.tagList.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));

    const matchesCategory = categoryFilter === 'all' || photo.category === categoryFilter;

    return matchesSearch && matchesCategory;
  });

  // Mock stats (replace with actual API call)
  const stats = {
    totalPhotos: photos.length,
    photosByCategory: [
      { category: 'Progress', count: 15 },
      { category: 'Materials', count: 8 },
      { category: 'Safety', count: 5 },
      { category: 'Equipment', count: 3 },
    ],
    recentPhotos: photos.slice(0, 6),
    totalSize: photos.reduce((sum, photo) => sum + photo.fileSize, 0),
  };

  // Handle photo upload
  const handlePhotoUpload = async (files: File[], metadata: any) => {
    try {
      // Mock upload logic (replace with actual API call)
      addToast({
        type: 'success',
        title: 'Photos Uploaded',
        message: `${files.length} photo(s) uploaded successfully.`,
      });
      setIsUploadModalOpen(false);
      setSelectedFiles([]);
    } catch (error: any) {
      addToast({
        type: 'error',
        title: 'Upload Failed',
        message: 'Failed to upload photos. Please try again.',
      });
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (!site) {
    return (
      <div className="text-center py-12">
        <div className="text-red-600 mb-4">
          <Image className="mx-auto h-12 w-12" />
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">Error loading photos</h3>
        <p className="text-gray-500 mb-4">Unable to load photo data for this site.</p>
        <Link to={`/sites/${siteId}`} className="text-blue-600 hover:text-blue-500">
          Back to Site Details
        </Link>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="md:flex md:items-center md:justify-between">
        <div className="flex-1 min-w-0">
          <div className="flex items-center">
            <Link
              to={`/sites/${siteId}`}
              className="mr-4 text-gray-400 hover:text-gray-600"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </Link>
            <div>
              <h2 className="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
                Photos - {site.name}
              </h2>
              <p className="mt-1 text-sm text-gray-500">
                Document progress and manage site photos.
              </p>
            </div>
          </div>
        </div>
        <div className="mt-4 flex md:mt-0 md:ml-4 space-x-3">
          <Button
            variant="outline"
            icon={<Download className="w-4 h-4" />}
          >
            Export
          </Button>
          <Button
            onClick={() => setIsUploadModalOpen(true)}
            icon={<Upload className="w-4 h-4" />}
          >
            Upload Photos
          </Button>
        </div>
      </div>

      {/* Statistics Cards - Coming Soon */}
      <Card>
        <CardContent className="p-8 text-center">
          <ImageIcon className="mx-auto h-12 w-12 text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Photo Statistics</h3>
          <p className="text-gray-500">Photo analytics and statistics coming soon.</p>
        </CardContent>
      </Card>

      {/* Drag and Drop Upload Area */}
      <Card>
        <CardContent className="p-6">
          <div
            {...getRootProps()}
            className={`border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors ${
              isDragActive
                ? 'border-blue-400 bg-blue-50'
                : 'border-gray-300 hover:border-gray-400'
            }`}
          >
            <input {...getInputProps()} />
            <Upload className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            {isDragActive ? (
              <p className="text-blue-600">Drop the photos here...</p>
            ) : (
              <div>
                <p className="text-gray-600 mb-2">
                  Drag and drop photos here, or click to select files
                </p>
                <p className="text-sm text-gray-500">
                  Supports: JPEG, PNG, GIF (max 10MB per file)
                </p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Filters and View Controls */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
            <div className="flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4">
              <Input
                placeholder="Search photos..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                leftIcon={<Filter className="w-4 h-4" />}
                className="sm:w-64"
              />

              <div>
                <select
                  value={categoryFilter}
                  onChange={(e) => setCategoryFilter(e.target.value as PhotoCategory | 'all')}
                  className="block w-full rounded-lg border border-gray-300 bg-white px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                >
                  <option value="all">All Categories</option>
                  {Object.entries(PhotoCategory).map(([key, value]) => (
                    <option key={key} value={value}>
                      {getCategoryLabel(value)}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <Button
                variant={viewMode === 'grid' ? 'primary' : 'outline'}
                size="sm"
                onClick={() => setViewMode('grid')}
                icon={<Grid className="w-4 h-4" />}
              >
                Grid
              </Button>
              <Button
                variant={viewMode === 'list' ? 'primary' : 'outline'}
                size="sm"
                onClick={() => setViewMode('list')}
                icon={<List className="w-4 h-4" />}
              >
                List
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Photos Display - Coming Soon */}
      {filteredPhotos.length > 0 ? (
        <Card>
          <CardContent className="p-8 text-center">
            <ImageIcon className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Photo Gallery</h3>
            <p className="text-gray-500">Advanced photo gallery with {filteredPhotos.length} photos coming soon.</p>
          </CardContent>
        </Card>
      ) : (
        <EmptyState
          icon={<Image className="mx-auto h-12 w-12 text-gray-400" />}
          title={searchQuery || categoryFilter !== 'all'
            ? 'No photos match your filters'
            : 'No photos uploaded yet'}
          description={searchQuery || categoryFilter !== 'all'
            ? 'Try adjusting your search criteria or filters.'
            : 'Start documenting your project by uploading your first photos.'}
          action={{
            label: 'Upload Photos',
            onClick: () => setIsUploadModalOpen(true),
          }}
        />
      )}

      {/* Upload Modal - Coming Soon */}
      {isUploadModalOpen && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={() => setIsUploadModalOpen(false)} />
            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
              <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div className="text-center">
                  <Upload className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Upload Photos</h3>
                  <p className="text-gray-500 mb-4">Photo upload functionality coming soon.</p>
                  <Button onClick={() => setIsUploadModalOpen(false)}>Close</Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
