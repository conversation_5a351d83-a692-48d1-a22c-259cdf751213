import React from 'react';
import { useParams } from 'react-router-dom';

export const PhotosPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();

  return (
    <div className="space-y-6">
      <h1 className="text-2xl font-bold text-gray-900">Photos</h1>
      <p className="text-gray-600">Site ID: {id}</p>
      <div className="card">
        <div className="card-content">
          <p>Photo management page coming soon...</p>
        </div>
      </div>
    </div>
  );
};
