import { apiClient, tokenManager } from './api';
import type { LoginRequest, RegisterRequest, AuthResponse, User } from '@/types';

export const authService = {
  // Login user
  login: async (credentials: LoginRequest): Promise<AuthResponse> => {
    const response = await apiClient.post<AuthResponse>('/auth/login', credentials);
    
    if (response.data.token) {
      tokenManager.setToken(response.data.token);
    }
    
    return response.data;
  },

  // Register new user and firm
  register: async (data: RegisterRequest): Promise<AuthResponse> => {
    const response = await apiClient.post<AuthResponse>('/auth/register', data);
    
    if (response.data.token) {
      tokenManager.setToken(response.data.token);
    }
    
    return response.data;
  },

  // Logout user
  logout: (): void => {
    tokenManager.removeToken();
  },

  // Get current user from token
  getCurrentUser: (): User | null => {
    const token = tokenManager.getToken();
    if (!token || !tokenManager.isTokenValid()) {
      return null;
    }

    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      return {
        id: payload.nameid || payload.sub,
        firstName: payload.given_name || '',
        lastName: payload.family_name || '',
        email: payload.email || '',
        role: parseInt(payload.role || '0'),
        firmId: payload.FirmId || '',
        firmName: payload.firm_name || '',
        isActive: true,
        lastLoginAt: payload.last_login || '',
        createdAt: payload.created_at || '',
      };
    } catch (error) {
      console.error('Error parsing token:', error);
      return null;
    }
  },

  // Check if user is authenticated
  isAuthenticated: (): boolean => {
    return tokenManager.isTokenValid();
  },

  // Get auth token
  getToken: (): string | null => {
    return tokenManager.getToken();
  },
};

export default authService;
