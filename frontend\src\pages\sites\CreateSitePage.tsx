import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useCreateSite } from '@/hooks/useSites';
import { useToast } from '@/components/ui/Toaster';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { SiteType, SiteStatus } from '@/types';
import type { CreateSiteRequest } from '@/types';

const createSiteSchema = z.object({
  name: z.string().min(1, 'Site name is required'),
  description: z.string().optional(),
  location: z.string().min(1, 'Location is required'),
  address: z.string().optional(),
  city: z.string().optional(),
  state: z.string().optional(),
  postalCode: z.string().optional(),
  area: z.number().positive('Area must be positive').optional(),
  areaUnit: z.string().optional(),
  type: z.number(),
  status: z.number(),
  startDate: z.string().optional(),
  expectedEndDate: z.string().optional(),
  estimatedBudget: z.number().positive('Budget must be positive').optional(),
  notes: z.string().optional(),
});

type CreateSiteFormData = z.infer<typeof createSiteSchema>;

export const CreateSitePage: React.FC = () => {
  const navigate = useNavigate();
  const { addToast } = useToast();
  const createSiteMutation = useCreateSite();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
  } = useForm<CreateSiteFormData>({
    resolver: zodResolver(createSiteSchema),
    defaultValues: {
      type: SiteType.Residential,
      status: SiteStatus.Planning,
      areaUnit: 'sqft',
    },
  });

  const onSubmit = async (data: CreateSiteFormData) => {
    try {
      setIsSubmitting(true);

      const siteData: CreateSiteRequest = {
        ...data,
        area: data.area || undefined,
        estimatedBudget: data.estimatedBudget || undefined,
        startDate: data.startDate || undefined,
        expectedEndDate: data.expectedEndDate || undefined,
      };

      const result = await createSiteMutation.mutateAsync(siteData);

      addToast({
        type: 'success',
        title: 'Site Created',
        message: 'Construction site has been created successfully.',
      });

      navigate(`/sites/${result.id}`);
    } catch (error: any) {
      addToast({
        type: 'error',
        title: 'Error',
        message: error.response?.data?.message || 'Failed to create site.',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const getSiteTypeLabel = (type: number): string => {
    const labels = {
      [SiteType.Residential]: 'Residential',
      [SiteType.Commercial]: 'Commercial',
      [SiteType.Industrial]: 'Industrial',
      [SiteType.Infrastructure]: 'Infrastructure',
      [SiteType.Renovation]: 'Renovation',
      [SiteType.Other]: 'Other',
    };
    return labels[type] || 'Unknown';
  };

  const getSiteStatusLabel = (status: number): string => {
    const labels = {
      [SiteStatus.Planning]: 'Planning',
      [SiteStatus.InProgress]: 'In Progress',
      [SiteStatus.OnHold]: 'On Hold',
      [SiteStatus.Completed]: 'Completed',
      [SiteStatus.Cancelled]: 'Cancelled',
    };
    return labels[status] || 'Unknown';
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="md:flex md:items-center md:justify-between">
        <div className="flex-1 min-w-0">
          <h2 className="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
            Create New Site
          </h2>
          <p className="mt-1 text-sm text-gray-500">
            Add a new construction site to your project portfolio.
          </p>
        </div>
        <div className="mt-4 flex md:mt-0 md:ml-4">
          <button
            type="button"
            onClick={() => navigate('/sites')}
            className="btn-secondary mr-3"
          >
            Cancel
          </button>
        </div>
      </div>

      {/* Form */}
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-medium text-gray-900">Basic Information</h3>
          </div>
          <div className="card-content">
            <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
              <div className="sm:col-span-2">
                <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                  Site Name *
                </label>
                <input
                  {...register('name')}
                  type="text"
                  className="mt-1 input"
                  placeholder="Enter site name"
                />
                {errors.name && (
                  <p className="mt-1 text-sm text-red-600">{errors.name.message}</p>
                )}
              </div>

              <div className="sm:col-span-2">
                <label htmlFor="description" className="block text-sm font-medium text-gray-700">
                  Description
                </label>
                <textarea
                  {...register('description')}
                  rows={3}
                  className="mt-1 input"
                  placeholder="Enter site description"
                />
                {errors.description && (
                  <p className="mt-1 text-sm text-red-600">{errors.description.message}</p>
                )}
              </div>

              <div>
                <label htmlFor="type" className="block text-sm font-medium text-gray-700">
                  Site Type *
                </label>
                <select
                  {...register('type', { valueAsNumber: true })}
                  className="mt-1 input"
                >
                  {Object.entries(SiteType).map(([key, value]) => (
                    <option key={key} value={value}>
                      {getSiteTypeLabel(value)}
                    </option>
                  ))}
                </select>
                {errors.type && (
                  <p className="mt-1 text-sm text-red-600">{errors.type.message}</p>
                )}
              </div>

              <div>
                <label htmlFor="status" className="block text-sm font-medium text-gray-700">
                  Status *
                </label>
                <select
                  {...register('status', { valueAsNumber: true })}
                  className="mt-1 input"
                >
                  {Object.entries(SiteStatus).map(([key, value]) => (
                    <option key={key} value={value}>
                      {getSiteStatusLabel(value)}
                    </option>
                  ))}
                </select>
                {errors.status && (
                  <p className="mt-1 text-sm text-red-600">{errors.status.message}</p>
                )}
              </div>
            </div>
          </div>
        </div>

        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-medium text-gray-900">Location Details</h3>
          </div>
          <div className="card-content">
            <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
              <div className="sm:col-span-2">
                <label htmlFor="location" className="block text-sm font-medium text-gray-700">
                  Location *
                </label>
                <input
                  {...register('location')}
                  type="text"
                  className="mt-1 input"
                  placeholder="Enter location"
                />
                {errors.location && (
                  <p className="mt-1 text-sm text-red-600">{errors.location.message}</p>
                )}
              </div>

              <div className="sm:col-span-2">
                <label htmlFor="address" className="block text-sm font-medium text-gray-700">
                  Address
                </label>
                <input
                  {...register('address')}
                  type="text"
                  className="mt-1 input"
                  placeholder="Enter full address"
                />
              </div>

              <div>
                <label htmlFor="city" className="block text-sm font-medium text-gray-700">
                  City
                </label>
                <input
                  {...register('city')}
                  type="text"
                  className="mt-1 input"
                  placeholder="Enter city"
                />
              </div>

              <div>
                <label htmlFor="state" className="block text-sm font-medium text-gray-700">
                  State/Province
                </label>
                <input
                  {...register('state')}
                  type="text"
                  className="mt-1 input"
                  placeholder="Enter state/province"
                />
              </div>

              <div>
                <label htmlFor="postalCode" className="block text-sm font-medium text-gray-700">
                  Postal Code
                </label>
                <input
                  {...register('postalCode')}
                  type="text"
                  className="mt-1 input"
                  placeholder="Enter postal code"
                />
              </div>

              <div>
                <label htmlFor="area" className="block text-sm font-medium text-gray-700">
                  Area
                </label>
                <div className="mt-1 flex rounded-md shadow-sm">
                  <input
                    {...register('area', { valueAsNumber: true })}
                    type="number"
                    step="0.01"
                    className="input rounded-r-none"
                    placeholder="0.00"
                  />
                  <select
                    {...register('areaUnit')}
                    className="input rounded-l-none border-l-0 w-24"
                  >
                    <option value="sqft">sq ft</option>
                    <option value="sqm">sq m</option>
                    <option value="acres">acres</option>
                  </select>
                </div>
                {errors.area && (
                  <p className="mt-1 text-sm text-red-600">{errors.area.message}</p>
                )}
              </div>
            </div>
          </div>
        </div>

        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-medium text-gray-900">Project Details</h3>
          </div>
          <div className="card-content">
            <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
              <div>
                <label htmlFor="startDate" className="block text-sm font-medium text-gray-700">
                  Start Date
                </label>
                <input
                  {...register('startDate')}
                  type="date"
                  className="mt-1 input"
                />
              </div>

              <div>
                <label htmlFor="expectedEndDate" className="block text-sm font-medium text-gray-700">
                  Expected End Date
                </label>
                <input
                  {...register('expectedEndDate')}
                  type="date"
                  className="mt-1 input"
                />
              </div>

              <div className="sm:col-span-2">
                <label htmlFor="estimatedBudget" className="block text-sm font-medium text-gray-700">
                  Estimated Budget
                </label>
                <div className="mt-1 relative rounded-md shadow-sm">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <span className="text-gray-500 sm:text-sm">$</span>
                  </div>
                  <input
                    {...register('estimatedBudget', { valueAsNumber: true })}
                    type="number"
                    step="0.01"
                    className="input pl-7"
                    placeholder="0.00"
                  />
                </div>
                {errors.estimatedBudget && (
                  <p className="mt-1 text-sm text-red-600">{errors.estimatedBudget.message}</p>
                )}
              </div>

              <div className="sm:col-span-2">
                <label htmlFor="notes" className="block text-sm font-medium text-gray-700">
                  Notes
                </label>
                <textarea
                  {...register('notes')}
                  rows={4}
                  className="mt-1 input"
                  placeholder="Enter any additional notes or comments"
                />
              </div>
            </div>
          </div>
        </div>

        {/* Submit Button */}
        <div className="flex justify-end space-x-3">
          <button
            type="button"
            onClick={() => navigate('/sites')}
            className="btn-secondary"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={isSubmitting}
            className="btn-primary"
          >
            {isSubmitting ? (
              <>
                <LoadingSpinner size="sm" className="mr-2" />
                Creating Site...
              </>
            ) : (
              'Create Site'
            )}
          </button>
        </div>
      </form>
    </div>
  );
};
