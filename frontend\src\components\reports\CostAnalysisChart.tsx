import React from 'react';
import {
  <PERSON><PERSON>hart,
  Line,
  XAxis,
  <PERSON>Axis,
  CartesianGrid,
  Tooltip,
  ResponsiveContaine<PERSON>,
  <PERSON>,
} from 'recharts';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/Card';

interface CostData {
  month: string;
  planned: number;
  actual: number;
}

interface CostAnalysisChartProps {
  data: CostData[];
}

export const CostAnalysisChart: React.FC<CostAnalysisChartProps> = ({ data }) => {
  const formatCurrency = (value: number): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-4 border border-gray-200 rounded-lg shadow-lg">
          <p className="font-medium text-gray-900 mb-2">{label}</p>
          {payload.map((entry: any, index: number) => (
            <p key={index} className="text-sm" style={{ color: entry.color }}>
              {entry.name}: {formatCurrency(entry.value)}
            </p>
          ))}
          {payload.length === 2 && (
            <p className="text-sm text-gray-600 mt-1 pt-1 border-t border-gray-200">
              Variance: {formatCurrency(payload[1].value - payload[0].value)}
            </p>
          )}
        </div>
      );
    }
    return null;
  };

  // Calculate totals and variance
  const totalPlanned = data.reduce((sum, item) => sum + item.planned, 0);
  const totalActual = data.reduce((sum, item) => sum + item.actual, 0);
  const totalVariance = totalActual - totalPlanned;
  const variancePercentage = ((totalVariance / totalPlanned) * 100).toFixed(1);

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          Cost Analysis: Planned vs Actual
          <div className="text-right">
            <div className="text-sm text-gray-500">Total Variance</div>
            <div className={`text-lg font-bold ${totalVariance >= 0 ? 'text-red-600' : 'text-green-600'}`}>
              {totalVariance >= 0 ? '+' : ''}{formatCurrency(totalVariance)}
            </div>
            <div className="text-xs text-gray-500">
              ({totalVariance >= 0 ? '+' : ''}{variancePercentage}%)
            </div>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="h-80 mb-6">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
              <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
              <XAxis 
                dataKey="month" 
                stroke="#6b7280"
                fontSize={12}
              />
              <YAxis 
                stroke="#6b7280"
                fontSize={12}
                tickFormatter={(value) => `$${(value / 1000).toFixed(0)}k`}
              />
              <Tooltip content={<CustomTooltip />} />
              <Legend />
              <Line
                type="monotone"
                dataKey="planned"
                stroke="#10b981"
                strokeWidth={3}
                dot={{ fill: '#10b981', strokeWidth: 2, r: 4 }}
                name="Planned"
              />
              <Line
                type="monotone"
                dataKey="actual"
                stroke="#3b82f6"
                strokeWidth={3}
                dot={{ fill: '#3b82f6', strokeWidth: 2, r: 4 }}
                name="Actual"
              />
            </LineChart>
          </ResponsiveContainer>
        </div>

        {/* Summary Statistics */}
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-3">
          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="text-sm font-medium text-gray-500">Total Planned</div>
            <div className="text-xl font-bold text-gray-900">{formatCurrency(totalPlanned)}</div>
          </div>
          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="text-sm font-medium text-gray-500">Total Actual</div>
            <div className="text-xl font-bold text-gray-900">{formatCurrency(totalActual)}</div>
          </div>
          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="text-sm font-medium text-gray-500">Efficiency</div>
            <div className={`text-xl font-bold ${totalVariance <= 0 ? 'text-green-600' : 'text-red-600'}`}>
              {totalVariance <= 0 ? 'Under Budget' : 'Over Budget'}
            </div>
            <div className="text-sm text-gray-600">
              by {formatCurrency(Math.abs(totalVariance))}
            </div>
          </div>
        </div>

        {/* Monthly Breakdown */}
        <div className="mt-6">
          <h4 className="text-sm font-medium text-gray-900 mb-3">Monthly Breakdown</h4>
          <div className="space-y-2">
            {data.map((item, index) => {
              const variance = item.actual - item.planned;
              const variancePercent = ((variance / item.planned) * 100).toFixed(1);
              
              return (
                <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center space-x-4">
                    <span className="font-medium text-gray-900 w-12">{item.month}</span>
                    <div className="text-sm text-gray-600">
                      Planned: {formatCurrency(item.planned)}
                    </div>
                    <div className="text-sm text-gray-600">
                      Actual: {formatCurrency(item.actual)}
                    </div>
                  </div>
                  <div className="text-right">
                    <div className={`text-sm font-medium ${variance >= 0 ? 'text-red-600' : 'text-green-600'}`}>
                      {variance >= 0 ? '+' : ''}{formatCurrency(variance)}
                    </div>
                    <div className="text-xs text-gray-500">
                      ({variance >= 0 ? '+' : ''}{variancePercent}%)
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
