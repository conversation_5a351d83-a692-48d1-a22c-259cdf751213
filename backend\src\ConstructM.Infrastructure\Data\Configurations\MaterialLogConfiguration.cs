using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using ConstructM.Domain.Entities;

namespace ConstructM.Infrastructure.Data.Configurations;

public class MaterialLogConfiguration : IEntityTypeConfiguration<MaterialLog>
{
    public void Configure(EntityTypeBuilder<MaterialLog> builder)
    {
        builder.ToTable("MaterialLogs");

        builder.HasKey(ml => ml.Id);

        builder.Property(ml => ml.MaterialName)
            .IsRequired()
            .HasMaxLength(200);

        builder.Property(ml => ml.Description)
            .HasMaxLength(500);

        builder.Property(ml => ml.Category)
            .IsRequired()
            .HasConversion<int>();

        builder.Property(ml => ml.Quantity)
            .IsRequired()
            .HasColumnType("decimal(18,4)");

        builder.Property(ml => ml.Unit)
            .IsRequired()
            .HasMaxLength(20);

        builder.Property(ml => ml.UnitRate)
            .IsRequired()
            .HasColumnType("decimal(18,2)");

        builder.Property(ml => ml.TotalCost)
            .HasColumnType("decimal(18,2)");

        builder.Property(ml => ml.LogType)
            .IsRequired()
            .HasConversion<int>();

        builder.Property(ml => ml.Supplier)
            .HasMaxLength(200);

        builder.Property(ml => ml.InvoiceNumber)
            .HasMaxLength(100);

        builder.Property(ml => ml.Notes)
            .HasMaxLength(1000);

        builder.Property(ml => ml.ReceiptUrl)
            .HasMaxLength(500);

        // Relationships
        builder.HasOne(ml => ml.Site)
            .WithMany(s => s.MaterialLogs)
            .HasForeignKey(ml => ml.SiteId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasOne(ml => ml.LoggedByUser)
            .WithMany(u => u.MaterialLogs)
            .HasForeignKey(ml => ml.LoggedByUserId)
            .OnDelete(DeleteBehavior.Restrict);

        // Indexes
        builder.HasIndex(ml => ml.SiteId);
        builder.HasIndex(ml => ml.LogDate);
        builder.HasIndex(ml => ml.Category);
        builder.HasIndex(ml => ml.LogType);
        builder.HasIndex(ml => ml.MaterialName);
    }
}
