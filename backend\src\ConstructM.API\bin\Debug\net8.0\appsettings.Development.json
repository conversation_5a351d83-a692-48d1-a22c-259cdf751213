{"ConnectionStrings": {"DefaultConnection": "Host=localhost;Database=constructm_dev;Username=constructm_user;Password=constructm_password"}, "JWT": {"SecretKey": "your-super-secret-jwt-key-for-development-only-change-in-production", "Issuer": "ConstructM", "Audience": "ConstructM-Users", "ExpirationInMinutes": 60}, "FileStorage": {"BasePath": "wwwroot/uploads", "MaxFileSizeInMB": 10, "AllowedExtensions": [".jpg", ".jpeg", ".png", ".pdf", ".dwg"]}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore.Database.Command": "Information"}}}