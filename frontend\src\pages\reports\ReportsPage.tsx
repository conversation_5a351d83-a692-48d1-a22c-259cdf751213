import React from 'react';
import { useParams } from 'react-router-dom';

export const ReportsPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();

  return (
    <div className="space-y-6">
      <h1 className="text-2xl font-bold text-gray-900">Reports</h1>
      <p className="text-gray-600">Site ID: {id}</p>
      <div className="card">
        <div className="card-content">
          <p>Reports and analytics page coming soon...</p>
        </div>
      </div>
    </div>
  );
};
