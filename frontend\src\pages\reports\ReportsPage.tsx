import React, { useState } from 'react';
import { use<PERSON>ara<PERSON>, <PERSON> } from 'react-router-dom';
import { Download, Calendar, Filter, TrendingUp, DollarSign, Package, Image as ImageIcon } from 'lucide-react';
import { useSite } from '@/hooks/useSites';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
// import { CostAnalysisChart } from '@/components/reports/CostAnalysisChart';
// import { MaterialUsageChart } from '@/components/reports/MaterialUsageChart';
// import { ProgressTimelineChart } from '@/components/reports/ProgressTimelineChart';
// import { ReportSummaryCards } from '@/components/reports/ReportSummaryCards';

export const ReportsPage: React.FC = () => {
  const { id: siteId } = useParams<{ id: string }>();
  const [dateRange, setDateRange] = useState({ start: '', end: '' });
  const [reportType, setReportType] = useState<'overview' | 'cost' | 'materials' | 'progress'>('overview');

  // API hooks
  const { data: site, isLoading: siteLoading } = useSite(siteId!);

  // Mock report data (replace with actual API calls)
  const reportData = {
    summary: {
      totalCost: 1890000,
      budgetVariance: -45000,
      materialsUsed: 1247,
      photosUploaded: 156,
      daysActive: 89,
      completionPercentage: 73,
    },
    costAnalysis: {
      monthly: [
        { month: 'Jan', planned: 150000, actual: 145000 },
        { month: 'Feb', planned: 180000, actual: 175000 },
        { month: 'Mar', planned: 200000, actual: 220000 },
        { month: 'Apr', planned: 170000, actual: 165000 },
        { month: 'May', planned: 190000, actual: 185000 },
        { month: 'Jun', planned: 160000, actual: 170000 },
      ],
      byCategory: [
        { category: 'Materials', amount: 850000, percentage: 45 },
        { category: 'Labor', amount: 604000, percentage: 32 },
        { category: 'Equipment', amount: 227000, percentage: 12 },
        { category: 'Other', amount: 209000, percentage: 11 },
      ],
    },
    materialUsage: [
      { material: 'Concrete', quantity: 450, unit: 'm³', cost: 135000 },
      { material: 'Steel Rebar', quantity: 25, unit: 'tons', cost: 87500 },
      { material: 'Lumber', quantity: 1200, unit: 'board ft', cost: 48000 },
      { material: 'Electrical Wire', quantity: 2500, unit: 'ft', cost: 12500 },
      { material: 'Plumbing Pipes', quantity: 800, unit: 'ft', cost: 16000 },
    ],
    progressTimeline: [
      { date: '2024-01-15', milestone: 'Foundation Started', progress: 10 },
      { date: '2024-02-01', milestone: 'Foundation Complete', progress: 25 },
      { date: '2024-02-15', milestone: 'Framing Started', progress: 35 },
      { date: '2024-03-01', milestone: 'Framing Complete', progress: 50 },
      { date: '2024-03-15', milestone: 'Roofing Started', progress: 60 },
      { date: '2024-04-01', milestone: 'Roofing Complete', progress: 73 },
    ],
  };

  const isLoading = siteLoading;

  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const handleExportReport = (format: 'pdf' | 'excel' | 'csv') => {
    // Mock export functionality
    console.log(`Exporting ${reportType} report as ${format}`);
    // In a real app, this would trigger a download
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (!site) {
    return (
      <div className="text-center py-12">
        <div className="text-red-600 mb-4">
          <TrendingUp className="mx-auto h-12 w-12" />
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">Error loading reports</h3>
        <p className="text-gray-500 mb-4">Unable to load report data for this site.</p>
        <Link to={`/sites/${siteId}`} className="text-blue-600 hover:text-blue-500">
          Back to Site Details
        </Link>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="md:flex md:items-center md:justify-between">
        <div className="flex-1 min-w-0">
          <div className="flex items-center">
            <Link
              to={`/sites/${siteId}`}
              className="mr-4 text-gray-400 hover:text-gray-600"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </Link>
            <div>
              <h2 className="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
                Reports - {site.name}
              </h2>
              <p className="mt-1 text-sm text-gray-500">
                Comprehensive analytics and cost analysis for this construction site.
              </p>
            </div>
          </div>
        </div>
        <div className="mt-4 flex md:mt-0 md:ml-4 space-x-3">
          <Button
            variant="outline"
            onClick={() => handleExportReport('pdf')}
            icon={<Download className="w-4 h-4" />}
          >
            Export PDF
          </Button>
          <Button
            variant="outline"
            onClick={() => handleExportReport('excel')}
            icon={<Download className="w-4 h-4" />}
          >
            Export Excel
          </Button>
        </div>
      </div>

      {/* Report Controls */}
      <Card>
        <CardContent className="p-4">
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Report Type
              </label>
              <select
                value={reportType}
                onChange={(e) => setReportType(e.target.value as any)}
                className="block w-full rounded-lg border border-gray-300 bg-white px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
              >
                <option value="overview">Overview</option>
                <option value="cost">Cost Analysis</option>
                <option value="materials">Materials</option>
                <option value="progress">Progress</option>
              </select>
            </div>

            <Input
              label="Start Date"
              type="date"
              value={dateRange.start}
              onChange={(e) => setDateRange(prev => ({ ...prev, start: e.target.value }))}
              leftIcon={<Calendar className="w-4 h-4" />}
            />

            <Input
              label="End Date"
              type="date"
              value={dateRange.end}
              onChange={(e) => setDateRange(prev => ({ ...prev, end: e.target.value }))}
              leftIcon={<Calendar className="w-4 h-4" />}
            />

            <div className="flex items-end">
              <Button
                variant="outline"
                icon={<Filter className="w-4 h-4" />}
                className="w-full"
              >
                Apply Filters
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Summary Cards - Coming Soon */}
      <Card>
        <CardContent className="p-8 text-center">
          <TrendingUp className="mx-auto h-12 w-12 text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Report Summary</h3>
          <p className="text-gray-500">Detailed report summaries coming soon.</p>
        </CardContent>
      </Card>

      {/* Report Content Based on Type - Coming Soon */}
      <Card>
        <CardHeader>
          <CardTitle>
            {reportType === 'overview' && 'Overview Report'}
            {reportType === 'cost' && 'Cost Analysis Report'}
            {reportType === 'materials' && 'Materials Report'}
            {reportType === 'progress' && 'Progress Report'}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-12">
            <TrendingUp className="mx-auto h-16 w-16 text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {reportType.charAt(0).toUpperCase() + reportType.slice(1)} Report
            </h3>
            <p className="text-gray-500 mb-4">
              Detailed {reportType} analytics and charts coming soon.
            </p>
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-3 max-w-md mx-auto">
              <div className="bg-gray-50 p-4 rounded-lg">
                <div className="text-sm text-gray-500">Charts</div>
                <div className="text-lg font-bold text-gray-900">Interactive</div>
              </div>
              <div className="bg-gray-50 p-4 rounded-lg">
                <div className="text-sm text-gray-500">Export</div>
                <div className="text-lg font-bold text-gray-900">PDF/Excel</div>
              </div>
              <div className="bg-gray-50 p-4 rounded-lg">
                <div className="text-sm text-gray-500">Real-time</div>
                <div className="text-lg font-bold text-gray-900">Data</div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
