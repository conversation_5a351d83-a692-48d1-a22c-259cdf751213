# ConstructM Backend API

.NET 8 Web API following Clean Architecture principles for the ConstructM construction site management system.

## 🏗️ Architecture

The backend follows Clean Architecture with the following layers:

- **ConstructM.API**: Web API layer (controllers, middleware, configuration)
- **ConstructM.Application**: Application layer (services, DTOs, interfaces)
- **ConstructM.Domain**: Domain layer (entities, value objects, domain services)
- **ConstructM.Infrastructure**: Infrastructure layer (data access, external services)
- **ConstructM.Shared**: Shared utilities and common code

## 🚀 Quick Start

### Prerequisites
- .NET 8 SDK
- PostgreSQL 14+ (or use Docker)

### Development Setup

1. **Start the database**:
```bash
# From project root
docker-compose -f docker-compose.dev.yml up postgres-dev -d
```

2. **Install Entity Framework CLI** (if not already installed):
```bash
dotnet tool install --global dotnet-ef
```

3. **Run database migrations**:
```bash
dotnet ef database update --project src/ConstructM.Infrastructure --startup-project src/ConstructM.API
```

4. **Start the API**:
```bash
dotnet run --project src/ConstructM.API
```

The API will be available at:
- HTTP: http://localhost:5000
- HTTPS: https://localhost:5001
- Swagger UI: http://localhost:5000/swagger

## 📦 Dependencies

### Core Packages
- ASP.NET Core 8.0
- Entity Framework Core 8.0
- PostgreSQL Entity Framework provider
- AutoMapper
- FluentValidation
- MediatR

### Authentication & Security
- JWT Bearer authentication
- ASP.NET Core Identity
- BCrypt for password hashing

### Documentation & Testing
- Swagger/OpenAPI
- xUnit
- Moq
- FluentAssertions

## 🔧 Configuration

### Environment Variables

Create `appsettings.Development.json` in the API project:

```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Host=localhost;Database=constructm_dev;Username=constructm_user;Password=constructm_password"
  },
  "JWT": {
    "SecretKey": "your-super-secret-jwt-key-for-development-only",
    "Issuer": "ConstructM",
    "Audience": "ConstructM-Users",
    "ExpirationInMinutes": 60
  },
  "FileStorage": {
    "BasePath": "wwwroot/uploads",
    "MaxFileSizeInMB": 10,
    "AllowedExtensions": [".jpg", ".jpeg", ".png", ".pdf", ".dwg"]
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  }
}
```

## 🗃️ Database

### Migrations

Create a new migration:
```bash
dotnet ef migrations add MigrationName --project src/ConstructM.Infrastructure --startup-project src/ConstructM.API
```

Update database:
```bash
dotnet ef database update --project src/ConstructM.Infrastructure --startup-project src/ConstructM.API
```

Remove last migration:
```bash
dotnet ef migrations remove --project src/ConstructM.Infrastructure --startup-project src/ConstructM.API
```

## 🧪 Testing

Run all tests:
```bash
dotnet test
```

Run specific test project:
```bash
dotnet test tests/ConstructM.UnitTests
dotnet test tests/ConstructM.IntegrationTests
```

## 📊 API Endpoints

### Authentication
- `POST /api/auth/register` - Register new firm
- `POST /api/auth/login` - User login
- `POST /api/auth/refresh` - Refresh JWT token

### Site Management
- `GET /api/sites` - List all sites for authenticated firm
- `POST /api/sites` - Create new construction site
- `GET /api/sites/{id}` - Get site details
- `PUT /api/sites/{id}` - Update site information
- `DELETE /api/sites/{id}` - Delete site

### Estimates
- `POST /api/sites/{id}/estimate` - Generate cost estimate
- `GET /api/sites/{id}/estimate` - Get current estimate

### Material Logging
- `GET /api/sites/{id}/materials` - Get material logs
- `POST /api/sites/{id}/materials` - Add material log entry
- `PUT /api/materials/{id}` - Update material log
- `DELETE /api/materials/{id}` - Delete material log

### Progress Photos
- `GET /api/sites/{id}/photos` - Get site photos
- `POST /api/sites/{id}/photos` - Upload progress photo
- `DELETE /api/photos/{id}` - Delete photo

### Reporting
- `GET /api/sites/{id}/report` - Get site summary report
- `GET /api/sites/{id}/report/pdf` - Export PDF report

## 🔒 Security

- JWT Bearer token authentication
- Role-based authorization
- Input validation with FluentValidation
- File upload security checks
- CORS configuration
- Rate limiting

## 📝 Development Notes

- Follow Clean Architecture principles
- Use CQRS pattern with MediatR
- Implement proper error handling
- Add comprehensive logging
- Write unit and integration tests
- Document API with XML comments
