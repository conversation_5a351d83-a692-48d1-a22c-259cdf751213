using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using ConstructM.Domain.Entities;

namespace ConstructM.Infrastructure.Data.Configurations;

public class PhotoConfiguration : IEntityTypeConfiguration<Photo>
{
    public void Configure(EntityTypeBuilder<Photo> builder)
    {
        builder.ToTable("Photos");

        builder.HasKey(p => p.Id);

        builder.Property(p => p.Title)
            .IsRequired()
            .HasMaxLength(200);

        builder.Property(p => p.Description)
            .HasMaxLength(1000);

        builder.Property(p => p.FilePath)
            .IsRequired()
            .HasMaxLength(500);

        builder.Property(p => p.FileName)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(p => p.ContentType)
            .IsRequired()
            .HasMaxLength(50);

        builder.Property(p => p.Category)
            .IsRequired()
            .HasConversion<int>();

        builder.Property(p => p.Location)
            .HasMaxLength(200);

        builder.Property(p => p.Latitude)
            .HasColumnType("decimal(10,8)");

        builder.Property(p => p.Longitude)
            .HasColumnType("decimal(11,8)");

        builder.Property(p => p.Tags)
            .HasMaxLength(1000);

        builder.Property(p => p.ThumbnailPath)
            .HasMaxLength(500);

        // Relationships
        builder.HasOne(p => p.Site)
            .WithMany(s => s.Photos)
            .HasForeignKey(p => p.SiteId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasOne(p => p.UploadedByUser)
            .WithMany(u => u.Photos)
            .HasForeignKey(p => p.UploadedByUserId)
            .OnDelete(DeleteBehavior.Restrict);

        // Computed properties (ignored)
        builder.Ignore(p => p.TagList);
        builder.Ignore(p => p.FileSizeFormatted);

        // Indexes
        builder.HasIndex(p => p.SiteId);
        builder.HasIndex(p => p.PhotoTakenAt);
        builder.HasIndex(p => p.Category);
        builder.HasIndex(p => p.IsPublic);
        builder.HasIndex(p => p.IsFeatured);
    }
}
