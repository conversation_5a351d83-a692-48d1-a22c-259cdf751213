import React from 'react';
import { Eye, Download, Trash2, Star, MapPin, Calendar } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';
import { Button } from '@/components/ui/Button';

interface Photo {
  id: string;
  title: string;
  description?: string;
  filePath: string;
  fileName: string;
  contentType: string;
  fileSize: number;
  category: number;
  photoTakenAt?: string;
  location?: string;
  tags?: string;
  isPublic: boolean;
  isFeatured: boolean;
  createdAt: string;
  uploadedBy: string;
  fileSizeFormatted: string;
  tagList: string[];
}

interface PhotoGalleryProps {
  photos: Photo[];
  viewMode: 'grid' | 'list';
  onPhotoClick: (photo: Photo) => void;
  onPhotoDelete: (photoId: string) => void;
}

export const PhotoGallery: React.FC<PhotoGalleryProps> = ({
  photos,
  viewMode,
  onPhotoClick,
  onPhotoDelete,
}) => {
  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const getCategoryBadgeVariant = (category: number): 'success' | 'warning' | 'error' | 'info' | 'default' => {
    // Map categories to badge variants
    const variants: Record<number, 'success' | 'warning' | 'error' | 'info' | 'default'> = {
      0: 'info',     // Progress
      1: 'success',  // Before/After
      2: 'warning',  // Materials
      3: 'default',  // Equipment
      4: 'error',    // Safety
      5: 'success',  // Quality
      6: 'info',     // Documentation
      7: 'default',  // Other
    };
    return variants[category] || 'default';
  };

  const getCategoryLabel = (category: number): string => {
    const labels: Record<number, string> = {
      0: 'Progress',
      1: 'Before/After',
      2: 'Materials',
      3: 'Equipment',
      4: 'Safety',
      5: 'Quality',
      6: 'Documentation',
      7: 'Other',
    };
    return labels[category] || 'Unknown';
  };

  if (viewMode === 'grid') {
    return (
      <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
        {photos.map((photo) => (
          <Card key={photo.id} className="overflow-hidden hover:shadow-lg transition-shadow">
            <div className="relative">
              <img
                src={photo.filePath}
                alt={photo.title}
                className="w-full h-48 object-cover cursor-pointer"
                onClick={() => onPhotoClick(photo)}
                onError={(e) => {
                  // Fallback to placeholder image
                  (e.target as HTMLImageElement).src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik04MCA2MEgxMjBWMTAwSDgwVjYwWiIgZmlsbD0iIzlDQTNBRiIvPgo8cGF0aCBkPSJNNjAgMTIwSDE0MFYxNDBINjBWMTIwWiIgZmlsbD0iIzlDQTNBRiIvPgo8L3N2Zz4K';
                }}
              />
              {photo.isFeatured && (
                <div className="absolute top-2 left-2">
                  <Badge variant="warning" size="sm">
                    <Star className="w-3 h-3 mr-1" />
                    Featured
                  </Badge>
                </div>
              )}
              <div className="absolute top-2 right-2">
                <Badge variant={getCategoryBadgeVariant(photo.category)} size="sm">
                  {getCategoryLabel(photo.category)}
                </Badge>
              </div>
              <div className="absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-20 transition-all duration-200 flex items-center justify-center opacity-0 hover:opacity-100">
                <div className="flex space-x-2">
                  <Button
                    size="sm"
                    variant="secondary"
                    onClick={(e) => {
                      e.stopPropagation();
                      onPhotoClick(photo);
                    }}
                    icon={<Eye className="w-4 h-4" />}
                  >
                    View
                  </Button>
                  <Button
                    size="sm"
                    variant="secondary"
                    onClick={(e) => {
                      e.stopPropagation();
                      // Handle download
                    }}
                    icon={<Download className="w-4 h-4" />}
                  >
                    Download
                  </Button>
                </div>
              </div>
            </div>
            <CardContent className="p-4">
              <div className="space-y-2">
                <h3 className="font-medium text-gray-900 truncate">{photo.title}</h3>
                {photo.description && (
                  <p className="text-sm text-gray-600 line-clamp-2">{photo.description}</p>
                )}
                <div className="flex items-center justify-between text-xs text-gray-500">
                  <div className="flex items-center space-x-2">
                    {photo.location && (
                      <div className="flex items-center">
                        <MapPin className="w-3 h-3 mr-1" />
                        <span className="truncate max-w-20">{photo.location}</span>
                      </div>
                    )}
                    <div className="flex items-center">
                      <Calendar className="w-3 h-3 mr-1" />
                      <span>{formatDate(photo.createdAt)}</span>
                    </div>
                  </div>
                  <span>{photo.fileSizeFormatted}</span>
                </div>
                {photo.tagList.length > 0 && (
                  <div className="flex flex-wrap gap-1">
                    {photo.tagList.slice(0, 3).map((tag, index) => (
                      <Badge key={index} variant="default" size="sm">
                        {tag}
                      </Badge>
                    ))}
                    {photo.tagList.length > 3 && (
                      <Badge variant="default" size="sm">
                        +{photo.tagList.length - 3}
                      </Badge>
                    )}
                  </div>
                )}
                <div className="flex items-center justify-between pt-2">
                  <span className="text-xs text-gray-500">by {photo.uploadedBy}</span>
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => onPhotoDelete(photo.id)}
                    icon={<Trash2 className="w-3 h-3" />}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  // List view
  return (
    <Card>
      <CardContent className="p-0">
        <div className="overflow-hidden">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Photo
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Category
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Location
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Size
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Date
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {photos.map((photo) => (
                <tr key={photo.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-12 w-12">
                        <img
                          className="h-12 w-12 rounded-lg object-cover"
                          src={photo.filePath}
                          alt={photo.title}
                          onError={(e) => {
                            (e.target as HTMLImageElement).src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDgiIGhlaWdodD0iNDgiIHZpZXdCb3g9IjAgMCA0OCA0OCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQ4IiBoZWlnaHQ9IjQ4IiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0yMCAxNkgyOFYyNEgyMFYxNloiIGZpbGw9IiM5Q0EzQUYiLz4KPHBhdGggZD0iTTE2IDI4SDMyVjMySDEyVjI4WiIgZmlsbD0iIzlDQTNBRiIvPgo8L3N2Zz4K';
                          }}
                        />
                      </div>
                      <div className="ml-4">
                        <div className="text-sm font-medium text-gray-900">{photo.title}</div>
                        {photo.description && (
                          <div className="text-sm text-gray-500 truncate max-w-xs">
                            {photo.description}
                          </div>
                        )}
                        <div className="text-xs text-gray-400">
                          {photo.fileName} • by {photo.uploadedBy}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <Badge variant={getCategoryBadgeVariant(photo.category)}>
                      {getCategoryLabel(photo.category)}
                    </Badge>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {photo.location || 'N/A'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {photo.fileSizeFormatted}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {formatDate(photo.createdAt)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex space-x-2">
                      <button
                        onClick={() => onPhotoClick(photo)}
                        className="text-blue-600 hover:text-blue-900"
                      >
                        View
                      </button>
                      <button
                        onClick={() => onPhotoDelete(photo.id)}
                        className="text-red-600 hover:text-red-900"
                      >
                        Delete
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </CardContent>
    </Card>
  );
};
