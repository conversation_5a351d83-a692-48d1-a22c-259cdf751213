// User and Authentication Types
export interface User {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  role: UserRole;
  firmId: string;
  firmName: string;
  isActive: boolean;
  lastLoginAt?: string;
  createdAt: string;
}

export const UserRole = {
  User: 0,
  Admin: 1,
  SuperAdmin: 2
} as const;

export type UserRole = typeof UserRole[keyof typeof UserRole];

export interface LoginRequest {
  email: string;
  password: string;
}

export interface RegisterRequest {
  firstName: string;
  lastName: string;
  email: string;
  password: string;
  phoneNumber?: string;
  firmName: string;
  firmEmail: string;
  firmPhone?: string;
  firmAddress?: string;
  firmCity?: string;
  firmState?: string;
  firmPostalCode?: string;
  firmCountry?: string;
}

export interface AuthResponse {
  message: string;
  token: string;
  user: User;
}

// Site Types
export interface Site {
  id: string;
  name: string;
  description?: string;
  location: string;
  address?: string;
  city?: string;
  state?: string;
  postalCode?: string;
  area?: number;
  areaUnit: string;
  type: SiteType;
  status: SiteStatus;
  startDate?: string;
  expectedEndDate?: string;
  actualEndDate?: string;
  estimatedBudget?: number;
  actualCost?: number;
  drawingUrl?: string;
  drawingFileName?: string;
  notes?: string;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  budgetVariance: number;
  budgetVariancePercentage: number;
  estimatesCount?: number;
  materialLogsCount?: number;
  photosCount?: number;
}

export enum SiteType {
  Residential = 0,
  Commercial = 1,
  Industrial = 2,
  Infrastructure = 3,
  Renovation = 4,
  Other = 5
}

export enum SiteStatus {
  Planning = 0,
  InProgress = 1,
  OnHold = 2,
  Completed = 3,
  Cancelled = 4
}

export interface CreateSiteRequest {
  name: string;
  description?: string;
  location: string;
  address?: string;
  city?: string;
  state?: string;
  postalCode?: string;
  area?: number;
  areaUnit?: string;
  type: SiteType;
  status: SiteStatus;
  startDate?: string;
  expectedEndDate?: string;
  estimatedBudget?: number;
  notes?: string;
}

export interface UpdateSiteRequest extends CreateSiteRequest {
  actualEndDate?: string;
  actualCost?: number;
}

// Material Types
export interface MaterialLog {
  id: string;
  materialName: string;
  description?: string;
  category: MaterialCategory;
  quantity: number;
  unit: string;
  unitRate: number;
  totalCost: number;
  logDate: string;
  logType: MaterialLogType;
  supplier?: string;
  invoiceNumber?: string;
  notes?: string;
  receiptUrl?: string;
  createdAt: string;
  loggedBy: string;
  siteName?: string;
}

export enum MaterialCategory {
  Concrete = 0,
  Steel = 1,
  Wood = 2,
  Brick = 3,
  Tile = 4,
  Paint = 5,
  Electrical = 6,
  Plumbing = 7,
  Roofing = 8,
  Insulation = 9,
  Hardware = 10,
  Tools = 11,
  Equipment = 12,
  Other = 13
}

export enum MaterialLogType {
  Purchase = 0,
  Usage = 1,
  Return = 2,
  Waste = 3,
  Transfer = 4
}

export interface CreateMaterialLogRequest {
  materialName: string;
  description?: string;
  category: MaterialCategory;
  quantity: number;
  unit: string;
  unitRate: number;
  logDate?: string;
  logType: MaterialLogType;
  supplier?: string;
  invoiceNumber?: string;
  notes?: string;
}

// Photo Types
export interface Photo {
  id: string;
  title: string;
  description?: string;
  filePath: string;
  fileName: string;
  contentType: string;
  fileSize: number;
  width?: number;
  height?: number;
  category: PhotoCategory;
  photoTakenAt: string;
  location?: string;
  latitude?: number;
  longitude?: number;
  tags?: string;
  isPublic: boolean;
  isFeatured: boolean;
  thumbnailPath?: string;
  createdAt: string;
  uploadedBy: string;
  siteName?: string;
  fileSizeFormatted: string;
  tagList: string[];
}

export enum PhotoCategory {
  Progress = 0,
  BeforeAfter = 1,
  Materials = 2,
  Equipment = 3,
  Safety = 4,
  Quality = 5,
  Documentation = 6,
  Other = 7
}

// Report Types
export interface SiteSummary {
  site: {
    id: string;
    name: string;
    status: SiteStatus;
    type: SiteType;
    estimatedBudget?: number;
    actualCost?: number;
    budgetVariance: number;
    budgetVariancePercentage: number;
    startDate?: string;
    expectedEndDate?: string;
    actualEndDate?: string;
  };
  statistics: {
    totalMaterialCost: number;
    materialLogCount: number;
    photoCount: number;
    estimateCount: number;
    daysInProgress: number;
    completionPercentage: number;
  };
  materialCostsByCategory: Array<{
    category: string;
    totalCost: number;
    totalQuantity: number;
    itemCount: number;
  }>;
  monthlySpending: Array<{
    year: number;
    month: number;
    totalCost: number;
    itemCount: number;
  }>;
  recentActivities: {
    materialLogs: Array<{
      id: string;
      materialName: string;
      category: MaterialCategory;
      totalCost: number;
      logDate: string;
      loggedBy: string;
    }>;
    photos: Array<{
      id: string;
      title: string;
      category: PhotoCategory;
      photoTakenAt: string;
      thumbnailPath?: string;
      uploadedBy: string;
    }>;
  };
}

export interface MaterialTrend {
  category: string;
  year: number;
  month: number;
  totalCost: number;
  totalQuantity: number;
  itemCount: number;
}

export interface CostAnalysis {
  budget: {
    estimated: number;
    actual: number;
    variance: number;
    variancePercentage: number;
  };
  materials: {
    estimated: number;
    actual: number;
    variance: number;
    variancePercentage: number;
  };
  breakdown: {
    materialCost: number;
    laborCost: number;
    equipmentCost: number;
    overheadCost: number;
    profitMargin: number;
  };
}

// API Response Types
export interface ApiResponse<T = any> {
  data?: T;
  message?: string;
  error?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

// UI State Types
export interface LoadingState {
  isLoading: boolean;
  error?: string;
}

export interface FormState<T> extends LoadingState {
  data: T;
  isDirty: boolean;
  isValid: boolean;
}
