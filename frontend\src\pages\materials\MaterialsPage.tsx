import React, { useState } from 'react';
import { useParams, Link } from 'react-router-dom';
import { Plus, Filter, Search, Download, Calendar, Package } from 'lucide-react';
import { useMaterialLogs, useMaterialStatistics, useCreateMaterialLog, useDeleteMaterialLog } from '@/hooks/useMaterials';
import { useSite } from '@/hooks/useSites';
import { useToast } from '@/components/ui/Toaster';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';
import { EmptyState } from '@/components/ui/EmptyState';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { MaterialCategory, MaterialLogType } from '@/types';
import type { CreateMaterialLogRequest } from '@/types';
// import { AddMaterialModal } from '@/components/materials/AddMaterialModal';
// import { MaterialStatsCards } from '@/components/materials/MaterialStatsCards';

export const MaterialsPage: React.FC = () => {
  const { id: siteId } = useParams<{ id: string }>();
  const { addToast } = useToast();

  // State for filters and modals
  const [searchQuery, setSearchQuery] = useState('');
  const [categoryFilter, setCategoryFilter] = useState<MaterialCategory | 'all'>('all');
  const [typeFilter, setTypeFilter] = useState<MaterialLogType | 'all'>('all');
  const [dateFilter, setDateFilter] = useState('');
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [selectedMaterial, setSelectedMaterial] = useState<string | null>(null);

  // API hooks
  const { data: site, isLoading: siteLoading } = useSite(siteId!);
  const { data: materials, isLoading: materialsLoading, error } = useMaterialLogs(siteId!, {
    date: dateFilter || undefined,
    category: categoryFilter !== 'all' ? categoryFilter : undefined,
  });
  const { data: stats, isLoading: statsLoading } = useMaterialStatistics(siteId!);
  const createMaterialMutation = useCreateMaterialLog();
  const deleteMaterialMutation = useDeleteMaterialLog();

  const isLoading = siteLoading || materialsLoading || statsLoading;

  // Helper functions
  const getCategoryLabel = (category: MaterialCategory): string => {
    const labels = {
      [MaterialCategory.Concrete]: 'Concrete',
      [MaterialCategory.Steel]: 'Steel',
      [MaterialCategory.Wood]: 'Wood',
      [MaterialCategory.Brick]: 'Brick',
      [MaterialCategory.Tile]: 'Tile',
      [MaterialCategory.Paint]: 'Paint',
      [MaterialCategory.Electrical]: 'Electrical',
      [MaterialCategory.Plumbing]: 'Plumbing',
      [MaterialCategory.Roofing]: 'Roofing',
      [MaterialCategory.Insulation]: 'Insulation',
      [MaterialCategory.Hardware]: 'Hardware',
      [MaterialCategory.Tools]: 'Tools',
      [MaterialCategory.Equipment]: 'Equipment',
      [MaterialCategory.Other]: 'Other',
    };
    return labels[category] || 'Unknown';
  };

  const getTypeLabel = (type: MaterialLogType): string => {
    const labels = {
      [MaterialLogType.Purchase]: 'Purchase',
      [MaterialLogType.Usage]: 'Usage',
      [MaterialLogType.Return]: 'Return',
      [MaterialLogType.Waste]: 'Waste',
      [MaterialLogType.Transfer]: 'Transfer',
    };
    return labels[type] || 'Unknown';
  };

  const getTypeBadgeVariant = (type: MaterialLogType): 'success' | 'warning' | 'error' | 'info' | 'default' => {
    const variants = {
      [MaterialLogType.Purchase]: 'success' as const,
      [MaterialLogType.Usage]: 'info' as const,
      [MaterialLogType.Return]: 'warning' as const,
      [MaterialLogType.Waste]: 'error' as const,
      [MaterialLogType.Transfer]: 'default' as const,
    };
    return variants[type] || 'default';
  };

  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  // Filter materials based on search and filters
  const filteredMaterials = materials?.filter((material) => {
    const matchesSearch = searchQuery === '' ||
      material.materialName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      material.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      material.supplier?.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesType = typeFilter === 'all' || material.logType === typeFilter;

    return matchesSearch && matchesType;
  }) || [];

  // Handle material creation
  const handleCreateMaterial = async (data: CreateMaterialLogRequest) => {
    try {
      await createMaterialMutation.mutateAsync({ siteId: siteId!, data });
      addToast({
        type: 'success',
        title: 'Material Added',
        message: 'Material log has been created successfully.',
      });
      setIsAddModalOpen(false);
    } catch (error: any) {
      addToast({
        type: 'error',
        title: 'Error',
        message: error.response?.data?.message || 'Failed to create material log.',
      });
    }
  };

  // Handle material deletion
  const handleDeleteMaterial = async (materialId: string) => {
    if (!confirm('Are you sure you want to delete this material log?')) return;

    try {
      await deleteMaterialMutation.mutateAsync({ siteId: siteId!, id: materialId });
      addToast({
        type: 'success',
        title: 'Material Deleted',
        message: 'Material log has been deleted successfully.',
      });
    } catch (error: any) {
      addToast({
        type: 'error',
        title: 'Error',
        message: error.response?.data?.message || 'Failed to delete material log.',
      });
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (error || !site) {
    return (
      <div className="text-center py-12">
        <div className="text-red-600 mb-4">
          <Package className="mx-auto h-12 w-12" />
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">Error loading materials</h3>
        <p className="text-gray-500 mb-4">Unable to load material data for this site.</p>
        <Link to={`/sites/${siteId}`} className="text-blue-600 hover:text-blue-500">
          Back to Site Details
        </Link>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="md:flex md:items-center md:justify-between">
        <div className="flex-1 min-w-0">
          <div className="flex items-center">
            <Link
              to={`/sites/${siteId}`}
              className="mr-4 text-gray-400 hover:text-gray-600"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </Link>
            <div>
              <h2 className="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
                Materials - {site.name}
              </h2>
              <p className="mt-1 text-sm text-gray-500">
                Track and manage material usage for this construction site.
              </p>
            </div>
          </div>
        </div>
        <div className="mt-4 flex md:mt-0 md:ml-4 space-x-3">
          <Button
            variant="outline"
            icon={<Download className="w-4 h-4" />}
          >
            Export
          </Button>
          <Button
            onClick={() => setIsAddModalOpen(true)}
            icon={<Plus className="w-4 h-4" />}
          >
            Add Material
          </Button>
        </div>
      </div>

      {/* Statistics Cards - Coming Soon */}
      <Card>
        <CardContent className="p-8 text-center">
          <Package className="mx-auto h-12 w-12 text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Material Statistics</h3>
          <p className="text-gray-500">Detailed material analytics coming soon.</p>
        </CardContent>
      </Card>

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
            <Input
              placeholder="Search materials..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              leftIcon={<Search className="w-4 h-4" />}
            />

            <div>
              <select
                value={categoryFilter}
                onChange={(e) => setCategoryFilter(e.target.value as MaterialCategory | 'all')}
                className="block w-full rounded-lg border border-gray-300 bg-white px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
              >
                <option value="all">All Categories</option>
                {Object.entries(MaterialCategory).map(([key, value]) => (
                  <option key={key} value={value}>
                    {getCategoryLabel(value)}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <select
                value={typeFilter}
                onChange={(e) => setTypeFilter(e.target.value as MaterialLogType | 'all')}
                className="block w-full rounded-lg border border-gray-300 bg-white px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
              >
                <option value="all">All Types</option>
                {Object.entries(MaterialLogType).map(([key, value]) => (
                  <option key={key} value={value}>
                    {getTypeLabel(value)}
                  </option>
                ))}
              </select>
            </div>

            <Input
              type="date"
              value={dateFilter}
              onChange={(e) => setDateFilter(e.target.value)}
              leftIcon={<Calendar className="w-4 h-4" />}
            />
          </div>
        </CardContent>
      </Card>

      {/* Materials List */}
      {filteredMaterials.length > 0 ? (
        <Card>
          <CardContent className="p-0">
            <div className="overflow-hidden">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Material
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Category
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Type
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Quantity
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Cost
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Date
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredMaterials.map((material) => (
                    <tr key={material.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-gray-900">
                            {material.materialName}
                          </div>
                          {material.description && (
                            <div className="text-sm text-gray-500 truncate max-w-xs">
                              {material.description}
                            </div>
                          )}
                          {material.supplier && (
                            <div className="text-xs text-gray-400">
                              Supplier: {material.supplier}
                            </div>
                          )}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <Badge variant="default">
                          {getCategoryLabel(material.category)}
                        </Badge>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <Badge variant={getTypeBadgeVariant(material.logType)}>
                          {getTypeLabel(material.logType)}
                        </Badge>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {material.quantity.toLocaleString()} {material.unit}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          {formatCurrency(material.totalCost)}
                        </div>
                        <div className="text-xs text-gray-500">
                          {formatCurrency(material.unitRate)}/{material.unit}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {formatDate(material.logDate)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div className="flex space-x-2">
                          <button
                            onClick={() => setSelectedMaterial(material.id)}
                            className="text-blue-600 hover:text-blue-900"
                          >
                            Edit
                          </button>
                          <button
                            onClick={() => handleDeleteMaterial(material.id)}
                            className="text-red-600 hover:text-red-900"
                            disabled={deleteMaterialMutation.isPending}
                          >
                            Delete
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      ) : (
        <EmptyState
          icon={<Package className="mx-auto h-12 w-12 text-gray-400" />}
          title={searchQuery || categoryFilter !== 'all' || typeFilter !== 'all' || dateFilter
            ? 'No materials match your filters'
            : 'No materials logged yet'}
          description={searchQuery || categoryFilter !== 'all' || typeFilter !== 'all' || dateFilter
            ? 'Try adjusting your search criteria or filters.'
            : 'Start tracking materials by adding your first material log entry.'}
          action={{
            label: 'Add Material',
            onClick: () => setIsAddModalOpen(true),
          }}
        />
      )}

      {/* Add Material Modal - Coming Soon */}
      {isAddModalOpen && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={() => setIsAddModalOpen(false)} />
            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
              <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div className="text-center">
                  <Package className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Add Material</h3>
                  <p className="text-gray-500 mb-4">Material creation form coming soon.</p>
                  <Button onClick={() => setIsAddModalOpen(false)}>Close</Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
