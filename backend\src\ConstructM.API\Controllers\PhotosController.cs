using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using ConstructM.Infrastructure.Data;
using ConstructM.Domain.Entities;
using System.Security.Claims;

namespace ConstructM.API.Controllers;

[ApiController]
[Route("api/sites/{siteId}/[controller]")]
[Authorize]
public class PhotosController : ControllerBase
{
    private readonly ConstructMDbContext _context;
    private readonly ILogger<PhotosController> _logger;
    private readonly IWebHostEnvironment _environment;

    public PhotosController(
        ConstructMDbContext context, 
        ILogger<PhotosController> logger,
        IWebHostEnvironment environment)
    {
        _context = context;
        _logger = logger;
        _environment = environment;
    }

    [HttpGet]
    public async Task<IActionResult> GetPhotos(Guid siteId, [FromQuery] PhotoCategory? category)
    {
        try
        {
            var firmId = GetCurrentUserFirmId();
            
            // Verify site belongs to user's firm
            var siteExists = await _context.Sites
                .AnyAsync(s => s.Id == siteId && s.FirmId == firmId);
            
            if (!siteExists)
            {
                return NotFound(new { message = "Site not found" });
            }

            var query = _context.Photos
                .Where(p => p.SiteId == siteId);

            if (category.HasValue)
            {
                var categoryValue = category.Value;
                query = query.Where(p => p.Category == categoryValue);
            }

            query = query.Include(p => p.UploadedByUser);

            var photos = await query
                .OrderByDescending(p => p.PhotoTakenAt)
                .Select(p => new
                {
                    p.Id,
                    p.Title,
                    p.Description,
                    p.FilePath,
                    p.FileName,
                    p.ContentType,
                    p.FileSize,
                    p.Width,
                    p.Height,
                    p.Category,
                    p.PhotoTakenAt,
                    p.Location,
                    p.Latitude,
                    p.Longitude,
                    p.Tags,
                    p.IsPublic,
                    p.IsFeatured,
                    p.ThumbnailPath,
                    p.CreatedAt,
                    UploadedBy = p.UploadedByUser.FullName,
                    FileSizeFormatted = p.FileSizeFormatted,
                    TagList = p.TagList
                })
                .ToListAsync();

            return Ok(photos);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving photos for site {SiteId}", siteId);
            return StatusCode(500, new { message = "An error occurred while retrieving photos" });
        }
    }

    [HttpPost]
    public async Task<IActionResult> UploadPhoto(Guid siteId, [FromForm] UploadPhotoRequest request)
    {
        try
        {
            var firmId = GetCurrentUserFirmId();
            var userId = GetCurrentUserId();
            
            // Verify site belongs to user's firm
            var siteExists = await _context.Sites
                .AnyAsync(s => s.Id == siteId && s.FirmId == firmId);
            
            if (!siteExists)
            {
                return NotFound(new { message = "Site not found" });
            }

            if (request.File == null || request.File.Length == 0)
            {
                return BadRequest(new { message = "No file uploaded" });
            }

            // Validate file type
            var allowedTypes = new[] { "image/jpeg", "image/jpg", "image/png", "image/gif" };
            if (!allowedTypes.Contains(request.File.ContentType.ToLower()))
            {
                return BadRequest(new { message = "Invalid file type. Only JPEG, PNG, and GIF files are allowed." });
            }

            // Validate file size (10MB max)
            const long maxFileSize = 10 * 1024 * 1024;
            if (request.File.Length > maxFileSize)
            {
                return BadRequest(new { message = "File size exceeds 10MB limit" });
            }

            // Create uploads directory if it doesn't exist
            var uploadsPath = Path.Combine(_environment.WebRootPath ?? _environment.ContentRootPath, "uploads", "photos");
            Directory.CreateDirectory(uploadsPath);

            // Generate unique filename
            var fileExtension = Path.GetExtension(request.File.FileName);
            var fileName = $"{Guid.NewGuid()}{fileExtension}";
            var filePath = Path.Combine(uploadsPath, fileName);
            var relativePath = Path.Combine("uploads", "photos", fileName).Replace("\\", "/");

            // Save file
            using (var stream = new FileStream(filePath, FileMode.Create))
            {
                await request.File.CopyToAsync(stream);
            }

            // Create photo record
            var photo = new Photo
            {
                Title = request.Title ?? request.File.FileName,
                Description = request.Description,
                FilePath = relativePath,
                FileName = request.File.FileName,
                ContentType = request.File.ContentType,
                FileSize = request.File.Length,
                Category = request.Category,
                PhotoTakenAt = request.PhotoTakenAt ?? DateTime.UtcNow,
                Location = request.Location,
                Latitude = request.Latitude,
                Longitude = request.Longitude,
                Tags = request.Tags,
                IsPublic = request.IsPublic,
                SiteId = siteId,
                UploadedByUserId = userId
            };

            _context.Photos.Add(photo);
            await _context.SaveChangesAsync();

            return CreatedAtAction(nameof(GetPhoto), new { siteId, id = photo.Id }, 
                new { photo.Id, message = "Photo uploaded successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error uploading photo for site {SiteId}", siteId);
            return StatusCode(500, new { message = "An error occurred while uploading the photo" });
        }
    }

    [HttpGet("{id}")]
    public async Task<IActionResult> GetPhoto(Guid siteId, Guid id)
    {
        try
        {
            var firmId = GetCurrentUserFirmId();
            
            var photo = await _context.Photos
                .Where(p => p.Id == id && p.SiteId == siteId)
                .Include(p => p.Site)
                .Include(p => p.UploadedByUser)
                .Where(p => p.Site.FirmId == firmId)
                .Select(p => new
                {
                    p.Id,
                    p.Title,
                    p.Description,
                    p.FilePath,
                    p.FileName,
                    p.ContentType,
                    p.FileSize,
                    p.Width,
                    p.Height,
                    p.Category,
                    p.PhotoTakenAt,
                    p.Location,
                    p.Latitude,
                    p.Longitude,
                    p.Tags,
                    p.IsPublic,
                    p.IsFeatured,
                    p.ThumbnailPath,
                    p.CreatedAt,
                    p.UpdatedAt,
                    UploadedBy = p.UploadedByUser.FullName,
                    SiteName = p.Site.Name,
                    FileSizeFormatted = p.FileSizeFormatted,
                    TagList = p.TagList
                })
                .FirstOrDefaultAsync();

            if (photo == null)
            {
                return NotFound(new { message = "Photo not found" });
            }

            return Ok(photo);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving photo {PhotoId} for site {SiteId}", id, siteId);
            return StatusCode(500, new { message = "An error occurred while retrieving the photo" });
        }
    }

    [HttpDelete("{id}")]
    public async Task<IActionResult> DeletePhoto(Guid siteId, Guid id)
    {
        try
        {
            var firmId = GetCurrentUserFirmId();
            
            var photo = await _context.Photos
                .Include(p => p.Site)
                .FirstOrDefaultAsync(p => p.Id == id && p.SiteId == siteId && p.Site.FirmId == firmId);

            if (photo == null)
            {
                return NotFound(new { message = "Photo not found" });
            }

            // Delete physical file
            var fullPath = Path.Combine(_environment.WebRootPath ?? _environment.ContentRootPath, photo.FilePath);
            if (System.IO.File.Exists(fullPath))
            {
                System.IO.File.Delete(fullPath);
            }

            // Delete thumbnail if exists
            if (!string.IsNullOrEmpty(photo.ThumbnailPath))
            {
                var thumbnailPath = Path.Combine(_environment.WebRootPath ?? _environment.ContentRootPath, photo.ThumbnailPath);
                if (System.IO.File.Exists(thumbnailPath))
                {
                    System.IO.File.Delete(thumbnailPath);
                }
            }

            _context.Photos.Remove(photo);
            await _context.SaveChangesAsync();

            return Ok(new { message = "Photo deleted successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting photo {PhotoId} for site {SiteId}", id, siteId);
            return StatusCode(500, new { message = "An error occurred while deleting the photo" });
        }
    }

    private Guid GetCurrentUserFirmId()
    {
        var firmIdClaim = User.FindFirst("FirmId")?.Value;
        return Guid.Parse(firmIdClaim ?? throw new UnauthorizedAccessException("Firm ID not found in token"));
    }

    private Guid GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        return Guid.Parse(userIdClaim ?? throw new UnauthorizedAccessException("User ID not found in token"));
    }
}

public class UploadPhotoRequest
{
    public IFormFile File { get; set; } = null!;
    public string? Title { get; set; }
    public string? Description { get; set; }
    public PhotoCategory Category { get; set; } = PhotoCategory.Progress;
    public DateTime? PhotoTakenAt { get; set; }
    public string? Location { get; set; }
    public decimal? Latitude { get; set; }
    public decimal? Longitude { get; set; }
    public string? Tags { get; set; }
    public bool IsPublic { get; set; } = false;
}
