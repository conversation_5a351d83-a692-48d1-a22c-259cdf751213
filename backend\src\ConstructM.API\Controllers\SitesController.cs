using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using ConstructM.Infrastructure.Data;
using ConstructM.Domain.Entities;
using System.Security.Claims;

namespace ConstructM.API.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class SitesController : ControllerBase
{
    private readonly ConstructMDbContext _context;
    private readonly ILogger<SitesController> _logger;

    public SitesController(ConstructMDbContext context, ILogger<SitesController> logger)
    {
        _context = context;
        _logger = logger;
    }

    [HttpGet]
    public async Task<IActionResult> GetSites()
    {
        try
        {
            var firmId = GetCurrentUserFirmId();
            
            var sites = await _context.Sites
                .Where(s => s.FirmId == firmId)
                .Include(s => s.CreatedByUser)
                .OrderByDescending(s => s.CreatedAt)
                .Select(s => new
                {
                    s.Id,
                    s.Name,
                    s.Description,
                    s.Location,
                    s.Address,
                    s.City,
                    s.State,
                    s.PostalCode,
                    s.Area,
                    s.AreaUnit,
                    s.Type,
                    s.Status,
                    s.StartDate,
                    s.ExpectedEndDate,
                    s.ActualEndDate,
                    s.EstimatedBudget,
                    s.ActualCost,
                    s.DrawingUrl,
                    s.DrawingFileName,
                    s.Notes,
                    s.CreatedAt,
                    s.UpdatedAt,
                    CreatedBy = s.CreatedByUser.FullName,
                    BudgetVariance = s.BudgetVariance,
                    BudgetVariancePercentage = s.BudgetVariancePercentage
                })
                .ToListAsync();

            return Ok(sites);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving sites");
            return StatusCode(500, new { message = "An error occurred while retrieving sites" });
        }
    }

    [HttpGet("{id}")]
    public async Task<IActionResult> GetSite(Guid id)
    {
        try
        {
            var firmId = GetCurrentUserFirmId();
            
            var site = await _context.Sites
                .Where(s => s.Id == id && s.FirmId == firmId)
                .Include(s => s.CreatedByUser)
                .Include(s => s.Estimates)
                .Include(s => s.MaterialLogs)
                .Include(s => s.Photos)
                .Select(s => new
                {
                    s.Id,
                    s.Name,
                    s.Description,
                    s.Location,
                    s.Address,
                    s.City,
                    s.State,
                    s.PostalCode,
                    s.Area,
                    s.AreaUnit,
                    s.Type,
                    s.Status,
                    s.StartDate,
                    s.ExpectedEndDate,
                    s.ActualEndDate,
                    s.EstimatedBudget,
                    s.ActualCost,
                    s.DrawingUrl,
                    s.DrawingFileName,
                    s.Notes,
                    s.CreatedAt,
                    s.UpdatedAt,
                    CreatedBy = s.CreatedByUser.FullName,
                    BudgetVariance = s.BudgetVariance,
                    BudgetVariancePercentage = s.BudgetVariancePercentage,
                    EstimatesCount = s.Estimates.Count,
                    MaterialLogsCount = s.MaterialLogs.Count,
                    PhotosCount = s.Photos.Count
                })
                .FirstOrDefaultAsync();

            if (site == null)
            {
                return NotFound(new { message = "Site not found" });
            }

            return Ok(site);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving site {SiteId}", id);
            return StatusCode(500, new { message = "An error occurred while retrieving the site" });
        }
    }

    [HttpPost]
    public async Task<IActionResult> CreateSite([FromBody] CreateSiteRequest request)
    {
        try
        {
            var firmId = GetCurrentUserFirmId();
            var userId = GetCurrentUserId();

            var site = new Site
            {
                Name = request.Name,
                Description = request.Description,
                Location = request.Location,
                Address = request.Address,
                City = request.City,
                State = request.State,
                PostalCode = request.PostalCode,
                Area = request.Area,
                AreaUnit = request.AreaUnit ?? "sqft",
                Type = request.Type,
                Status = request.Status,
                StartDate = request.StartDate,
                ExpectedEndDate = request.ExpectedEndDate,
                EstimatedBudget = request.EstimatedBudget,
                Notes = request.Notes,
                FirmId = firmId,
                CreatedByUserId = userId
            };

            _context.Sites.Add(site);
            await _context.SaveChangesAsync();

            return CreatedAtAction(nameof(GetSite), new { id = site.Id }, new { site.Id, message = "Site created successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating site");
            return StatusCode(500, new { message = "An error occurred while creating the site" });
        }
    }

    [HttpPut("{id}")]
    public async Task<IActionResult> UpdateSite(Guid id, [FromBody] UpdateSiteRequest request)
    {
        try
        {
            var firmId = GetCurrentUserFirmId();
            
            var site = await _context.Sites
                .FirstOrDefaultAsync(s => s.Id == id && s.FirmId == firmId);

            if (site == null)
            {
                return NotFound(new { message = "Site not found" });
            }

            site.Name = request.Name;
            site.Description = request.Description;
            site.Location = request.Location;
            site.Address = request.Address;
            site.City = request.City;
            site.State = request.State;
            site.PostalCode = request.PostalCode;
            site.Area = request.Area;
            site.AreaUnit = request.AreaUnit ?? site.AreaUnit;
            site.Type = request.Type;
            site.Status = request.Status;
            site.StartDate = request.StartDate;
            site.ExpectedEndDate = request.ExpectedEndDate;
            site.ActualEndDate = request.ActualEndDate;
            site.EstimatedBudget = request.EstimatedBudget;
            site.ActualCost = request.ActualCost;
            site.Notes = request.Notes;

            await _context.SaveChangesAsync();

            return Ok(new { message = "Site updated successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating site {SiteId}", id);
            return StatusCode(500, new { message = "An error occurred while updating the site" });
        }
    }

    [HttpDelete("{id}")]
    public async Task<IActionResult> DeleteSite(Guid id)
    {
        try
        {
            var firmId = GetCurrentUserFirmId();
            
            var site = await _context.Sites
                .FirstOrDefaultAsync(s => s.Id == id && s.FirmId == firmId);

            if (site == null)
            {
                return NotFound(new { message = "Site not found" });
            }

            _context.Sites.Remove(site);
            await _context.SaveChangesAsync();

            return Ok(new { message = "Site deleted successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting site {SiteId}", id);
            return StatusCode(500, new { message = "An error occurred while deleting the site" });
        }
    }

    private Guid GetCurrentUserFirmId()
    {
        var firmIdClaim = User.FindFirst("FirmId")?.Value;
        return Guid.Parse(firmIdClaim ?? throw new UnauthorizedAccessException("Firm ID not found in token"));
    }

    private Guid GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        return Guid.Parse(userIdClaim ?? throw new UnauthorizedAccessException("User ID not found in token"));
    }
}

public class CreateSiteRequest
{
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public string Location { get; set; } = string.Empty;
    public string? Address { get; set; }
    public string? City { get; set; }
    public string? State { get; set; }
    public string? PostalCode { get; set; }
    public decimal? Area { get; set; }
    public string? AreaUnit { get; set; }
    public SiteType Type { get; set; }
    public SiteStatus Status { get; set; }
    public DateTime? StartDate { get; set; }
    public DateTime? ExpectedEndDate { get; set; }
    public decimal? EstimatedBudget { get; set; }
    public string? Notes { get; set; }
}

public class UpdateSiteRequest : CreateSiteRequest
{
    public DateTime? ActualEndDate { get; set; }
    public decimal? ActualCost { get; set; }
}
