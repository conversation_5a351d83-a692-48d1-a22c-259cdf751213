using Microsoft.EntityFrameworkCore;
using ConstructM.Domain.Entities;
using ConstructM.Domain.Common;
using System.Linq.Expressions;

namespace ConstructM.Infrastructure.Data;

public class ConstructMDbContext : DbContext
{
    public ConstructMDbContext(DbContextOptions<ConstructMDbContext> options) : base(options)
    {
    }

    // DbSets
    public DbSet<User> Users { get; set; }
    public DbSet<Firm> Firms { get; set; }
    public DbSet<Site> Sites { get; set; }
    public DbSet<Estimate> Estimates { get; set; }
    public DbSet<EstimateItem> EstimateItems { get; set; }
    public DbSet<MaterialLog> MaterialLogs { get; set; }
    public DbSet<Photo> Photos { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Apply configurations
        modelBuilder.ApplyConfigurationsFromAssembly(typeof(ConstructMDbContext).Assembly);

        // Global query filters for soft delete
        foreach (var entityType in modelBuilder.Model.GetEntityTypes())
        {
            if (typeof(BaseEntity).IsAssignableFrom(entityType.ClrType))
            {
                modelBuilder.Entity(entityType.ClrType)
                    .HasQueryFilter(GetSoftDeleteFilter(entityType.ClrType));
            }
        }

        // Configure decimal precision globally
        foreach (var property in modelBuilder.Model.GetEntityTypes()
            .SelectMany(t => t.GetProperties())
            .Where(p => p.ClrType == typeof(decimal) || p.ClrType == typeof(decimal?)))
        {
            property.SetColumnType("decimal(18,2)");
        }

        // Seed data
        SeedData(modelBuilder);
    }

    private static LambdaExpression GetSoftDeleteFilter(Type entityType)
    {
        var parameter = Expression.Parameter(entityType, "e");
        var property = Expression.Property(parameter, nameof(BaseEntity.IsDeleted));
        var condition = Expression.Equal(property, Expression.Constant(false));
        return Expression.Lambda(condition, parameter);
    }

    public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        UpdateAuditFields();
        return await base.SaveChangesAsync(cancellationToken);
    }

    public override int SaveChanges()
    {
        UpdateAuditFields();
        return base.SaveChanges();
    }

    private void UpdateAuditFields()
    {
        var entries = ChangeTracker.Entries<BaseEntity>();

        foreach (var entry in entries)
        {
            switch (entry.State)
            {
                case EntityState.Added:
                    entry.Entity.CreatedAt = DateTime.UtcNow;
                    entry.Entity.UpdatedAt = DateTime.UtcNow;
                    break;

                case EntityState.Modified:
                    entry.Entity.UpdatedAt = DateTime.UtcNow;
                    entry.Property(e => e.CreatedAt).IsModified = false;
                    entry.Property(e => e.CreatedBy).IsModified = false;
                    break;

                case EntityState.Deleted:
                    entry.State = EntityState.Modified;
                    entry.Entity.IsDeleted = true;
                    entry.Entity.DeletedAt = DateTime.UtcNow;
                    entry.Entity.UpdatedAt = DateTime.UtcNow;
                    break;
            }
        }
    }

    private static void SeedData(ModelBuilder modelBuilder)
    {
        // Seed default firm for development
        var defaultFirmId = Guid.Parse("11111111-1111-1111-1111-111111111111");
        modelBuilder.Entity<Firm>().HasData(
            new Firm
            {
                Id = defaultFirmId,
                Name = "Demo Construction Company",
                Description = "A demo construction company for testing purposes",
                ContactEmail = "<EMAIL>",
                ContactPhone = "******-0123",
                Address = "123 Construction Ave",
                City = "Demo City",
                State = "Demo State",
                PostalCode = "12345",
                Country = "Demo Country",
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            }
        );

        // Seed default admin user
        var defaultUserId = Guid.Parse("*************-2222-2222-************");
        modelBuilder.Entity<User>().HasData(
            new User
            {
                Id = defaultUserId,
                FirstName = "Admin",
                LastName = "User",
                Email = "<EMAIL>",
                PasswordHash = "$2a$11$rQZrHzQJlVJ5rJ5rJ5rJ5uJ5rJ5rJ5rJ5rJ5rJ5rJ5rJ5rJ5rJ5rJ", // "password123"
                Role = Domain.Entities.UserRole.Admin,
                IsEmailConfirmed = true,
                IsActive = true,
                FirmId = defaultFirmId,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            }
        );
    }
}
