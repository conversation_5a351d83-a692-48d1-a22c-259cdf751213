import { apiClient, uploadFile } from './api';
import type { Site, CreateSiteRequest, UpdateSiteRequest } from '@/types';

export const sitesService = {
  // Get all sites for the current firm
  getAll: async (): Promise<Site[]> => {
    const response = await apiClient.get<Site[]>('/sites');
    return response.data;
  },

  // Get a specific site by ID
  getById: async (id: string): Promise<Site> => {
    const response = await apiClient.get<Site>(`/sites/${id}`);
    return response.data;
  },

  // Create a new site
  create: async (data: CreateSiteRequest): Promise<{ id: string; message: string }> => {
    const response = await apiClient.post<{ id: string; message: string }>('/sites', data);
    return response.data;
  },

  // Update an existing site
  update: async (id: string, data: UpdateSiteRequest): Promise<{ message: string }> => {
    const response = await apiClient.put<{ message: string }>(`/sites/${id}`, data);
    return response.data;
  },

  // Delete a site
  delete: async (id: string): Promise<{ message: string }> => {
    const response = await apiClient.delete<{ message: string }>(`/sites/${id}`);
    return response.data;
  },

  // Upload site drawing
  uploadDrawing: async (
    siteId: string,
    file: File,
    onProgress?: (progress: number) => void
  ): Promise<{ message: string; url: string }> => {
    const response = await uploadFile(
      `/sites/${siteId}/drawing`,
      file,
      {},
      onProgress
    );
    return response.data;
  },

  // Get site statistics
  getStatistics: async (): Promise<{
    totalSites: number;
    activeSites: number;
    completedSites: number;
    totalBudget: number;
    totalSpent: number;
  }> => {
    const sites = await sitesService.getAll();
    
    const totalSites = sites.length;
    const activeSites = sites.filter(s => s.status === 1).length; // InProgress
    const completedSites = sites.filter(s => s.status === 3).length; // Completed
    const totalBudget = sites.reduce((sum, s) => sum + (s.estimatedBudget || 0), 0);
    const totalSpent = sites.reduce((sum, s) => sum + (s.actualCost || 0), 0);

    return {
      totalSites,
      activeSites,
      completedSites,
      totalBudget,
      totalSpent,
    };
  },

  // Get recent sites
  getRecent: async (limit: number = 5): Promise<Site[]> => {
    const sites = await sitesService.getAll();
    return sites
      .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
      .slice(0, limit);
  },

  // Search sites
  search: async (query: string): Promise<Site[]> => {
    const sites = await sitesService.getAll();
    const searchTerm = query.toLowerCase();
    
    return sites.filter(site => 
      site.name.toLowerCase().includes(searchTerm) ||
      site.location.toLowerCase().includes(searchTerm) ||
      site.description?.toLowerCase().includes(searchTerm) ||
      site.city?.toLowerCase().includes(searchTerm) ||
      site.state?.toLowerCase().includes(searchTerm)
    );
  },

  // Filter sites by status
  filterByStatus: async (status: number): Promise<Site[]> => {
    const sites = await sitesService.getAll();
    return sites.filter(site => site.status === status);
  },

  // Filter sites by type
  filterByType: async (type: number): Promise<Site[]> => {
    const sites = await sitesService.getAll();
    return sites.filter(site => site.type === type);
  },
};

export default sitesService;
