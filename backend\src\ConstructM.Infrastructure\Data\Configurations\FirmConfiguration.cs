using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using ConstructM.Domain.Entities;

namespace ConstructM.Infrastructure.Data.Configurations;

public class FirmConfiguration : IEntityTypeConfiguration<Firm>
{
    public void Configure(EntityTypeBuilder<Firm> builder)
    {
        builder.ToTable("Firms");

        builder.HasKey(f => f.Id);

        builder.Property(f => f.Name)
            .IsRequired()
            .HasMaxLength(200);

        builder.Property(f => f.Description)
            .HasMaxLength(500);

        builder.Property(f => f.ContactEmail)
            .IsRequired()
            .HasMaxLength(255);

        builder.HasIndex(f => f.ContactEmail)
            .IsUnique();

        builder.Property(f => f.ContactPhone)
            .HasMaxLength(20);

        builder.Property(f => f.Address)
            .HasMaxLength(500);

        builder.Property(f => f.City)
            .HasMaxLength(100);

        builder.Property(f => f.State)
            .HasMaxLength(100);

        builder.Property(f => f.PostalCode)
            .HasMaxLength(20);

        builder.Property(f => f.Country)
            .HasMaxLength(100);

        builder.Property(f => f.RegistrationNumber)
            .HasMaxLength(50);

        builder.Property(f => f.TaxId)
            .HasMaxLength(50);

        // Relationships
        builder.HasMany(f => f.Users)
            .WithOne(u => u.Firm)
            .HasForeignKey(u => u.FirmId)
            .OnDelete(DeleteBehavior.Restrict);

        builder.HasMany(f => f.Sites)
            .WithOne(s => s.Firm)
            .HasForeignKey(s => s.FirmId)
            .OnDelete(DeleteBehavior.Restrict);
    }
}
