D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\obj\Debug\net8.0\ConstructM.API.csproj.AssemblyReference.cache
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\obj\Debug\net8.0\ConstructM.API.GeneratedMSBuildEditorConfig.editorconfig
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\obj\Debug\net8.0\ConstructM.API.AssemblyInfoInputs.cache
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\obj\Debug\net8.0\ConstructM.API.AssemblyInfo.cs
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\obj\Debug\net8.0\ConstructM.API.csproj.CoreCompileInputs.cache
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\obj\Debug\net8.0\ConstructM.API.MvcApplicationPartsAssemblyInfo.cs
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\obj\Debug\net8.0\ConstructM.API.MvcApplicationPartsAssemblyInfo.cache
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\appsettings.Development.json
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\appsettings.json
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\ConstructM.API.exe
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\ConstructM.API.deps.json
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\ConstructM.API.runtimeconfig.json
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\ConstructM.API.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\ConstructM.API.pdb
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\Humanizer.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\Microsoft.AspNetCore.Authentication.JwtBearer.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\Microsoft.Bcl.AsyncInterfaces.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\Microsoft.Build.Locator.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\Microsoft.CodeAnalysis.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\Microsoft.CodeAnalysis.CSharp.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\Microsoft.CodeAnalysis.CSharp.Workspaces.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\Microsoft.CodeAnalysis.Workspaces.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\Microsoft.CodeAnalysis.Workspaces.MSBuild.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\Microsoft.EntityFrameworkCore.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\Microsoft.EntityFrameworkCore.Abstractions.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\Microsoft.EntityFrameworkCore.Design.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\Microsoft.EntityFrameworkCore.Relational.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\Microsoft.Extensions.Caching.Abstractions.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\Microsoft.Extensions.Caching.Memory.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\Microsoft.Extensions.Configuration.Abstractions.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\Microsoft.Extensions.DependencyInjection.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\Microsoft.Extensions.DependencyInjection.Abstractions.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\Microsoft.Extensions.DependencyModel.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\Microsoft.Extensions.Logging.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\Microsoft.Extensions.Logging.Abstractions.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\Microsoft.Extensions.Options.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\Microsoft.Extensions.Primitives.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\Microsoft.IdentityModel.Abstractions.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\Microsoft.IdentityModel.JsonWebTokens.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\Microsoft.IdentityModel.Logging.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\Microsoft.IdentityModel.Protocols.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\Microsoft.IdentityModel.Protocols.OpenIdConnect.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\Microsoft.IdentityModel.Tokens.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\Microsoft.OpenApi.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\Mono.TextTemplating.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\Npgsql.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\Npgsql.EntityFrameworkCore.PostgreSQL.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\Swashbuckle.AspNetCore.Swagger.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\Swashbuckle.AspNetCore.SwaggerGen.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\Swashbuckle.AspNetCore.SwaggerUI.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\System.CodeDom.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\System.Composition.AttributedModel.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\System.Composition.Convention.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\System.Composition.Hosting.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\System.Composition.Runtime.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\System.Composition.TypedParts.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\System.Diagnostics.DiagnosticSource.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\System.IdentityModel.Tokens.Jwt.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\System.IO.Pipelines.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\System.Text.Encodings.Web.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\System.Text.Json.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\cs\Microsoft.CodeAnalysis.resources.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\de\Microsoft.CodeAnalysis.resources.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\es\Microsoft.CodeAnalysis.resources.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\fr\Microsoft.CodeAnalysis.resources.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\it\Microsoft.CodeAnalysis.resources.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\ja\Microsoft.CodeAnalysis.resources.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\ko\Microsoft.CodeAnalysis.resources.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\pl\Microsoft.CodeAnalysis.resources.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\pt-BR\Microsoft.CodeAnalysis.resources.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\ru\Microsoft.CodeAnalysis.resources.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\tr\Microsoft.CodeAnalysis.resources.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\zh-Hans\Microsoft.CodeAnalysis.resources.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\zh-Hant\Microsoft.CodeAnalysis.resources.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\cs\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\de\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\es\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\fr\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\it\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\ja\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\ko\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\pl\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\pt-BR\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\ru\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\tr\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\zh-Hans\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\zh-Hant\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\cs\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\de\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\es\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\fr\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\it\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\ja\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\ko\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\pl\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\pt-BR\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\ru\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\tr\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\zh-Hans\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\zh-Hant\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\cs\Microsoft.CodeAnalysis.Workspaces.resources.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\de\Microsoft.CodeAnalysis.Workspaces.resources.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\es\Microsoft.CodeAnalysis.Workspaces.resources.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\fr\Microsoft.CodeAnalysis.Workspaces.resources.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\it\Microsoft.CodeAnalysis.Workspaces.resources.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\ja\Microsoft.CodeAnalysis.Workspaces.resources.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\ko\Microsoft.CodeAnalysis.Workspaces.resources.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\pl\Microsoft.CodeAnalysis.Workspaces.resources.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\pt-BR\Microsoft.CodeAnalysis.Workspaces.resources.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\ru\Microsoft.CodeAnalysis.Workspaces.resources.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\tr\Microsoft.CodeAnalysis.Workspaces.resources.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\zh-Hans\Microsoft.CodeAnalysis.Workspaces.resources.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\zh-Hant\Microsoft.CodeAnalysis.Workspaces.resources.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\cs\Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\de\Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\es\Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\fr\Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\it\Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\ja\Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\ko\Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\pl\Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\pt-BR\Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\ru\Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\tr\Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\zh-Hans\Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\zh-Hant\Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\runtimes\browser\lib\net8.0\System.Text.Encodings.Web.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\ConstructM.Domain.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\ConstructM.Infrastructure.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\ConstructM.Infrastructure.pdb
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\bin\Debug\net8.0\ConstructM.Domain.pdb
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\obj\Debug\net8.0\staticwebassets.build.json
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\obj\Debug\net8.0\staticwebassets.development.json
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\obj\Debug\net8.0\staticwebassets\msbuild.ConstructM.API.Microsoft.AspNetCore.StaticWebAssets.props
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\obj\Debug\net8.0\staticwebassets\msbuild.build.ConstructM.API.props
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\obj\Debug\net8.0\staticwebassets\msbuild.buildMultiTargeting.ConstructM.API.props
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\obj\Debug\net8.0\staticwebassets\msbuild.buildTransitive.ConstructM.API.props
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\obj\Debug\net8.0\staticwebassets.pack.json
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\obj\Debug\net8.0\scopedcss\bundle\ConstructM.API.styles.css
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\obj\Debug\net8.0\Construc.BE20A040.Up2Date
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\obj\Debug\net8.0\ConstructM.API.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\obj\Debug\net8.0\refint\ConstructM.API.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\obj\Debug\net8.0\ConstructM.API.pdb
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\obj\Debug\net8.0\ConstructM.API.genruntimeconfig.cache
D:\DataOps Sync\Construct-M\backend\src\ConstructM.API\obj\Debug\net8.0\ref\ConstructM.API.dll
