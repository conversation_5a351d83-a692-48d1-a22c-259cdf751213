import React from 'react';
import { DollarSign, TrendingUp, Package, Image, Calendar, Target } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/Card';

interface ReportSummaryData {
  totalCost: number;
  budgetVariance: number;
  materialsUsed: number;
  photosUploaded: number;
  daysActive: number;
  completionPercentage: number;
}

interface ReportSummaryCardsProps {
  data: ReportSummaryData;
}

export const ReportSummaryCards: React.FC<ReportSummaryCardsProps> = ({ data }) => {
  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const getVarianceColor = (variance: number): string => {
    if (variance > 0) return 'text-red-600';
    if (variance < 0) return 'text-green-600';
    return 'text-gray-600';
  };

  const getVarianceIcon = (variance: number) => {
    if (variance > 0) return '↗';
    if (variance < 0) return '↘';
    return '→';
  };

  return (
    <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6">
      <Card hover>
        <CardContent className="p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
                <DollarSign className="w-5 h-5 text-white" />
              </div>
            </div>
            <div className="ml-5 w-0 flex-1">
              <dl>
                <dt className="text-sm font-medium text-gray-500 truncate">
                  Total Cost
                </dt>
                <dd className="text-lg font-bold text-gray-900">
                  {formatCurrency(data.totalCost)}
                </dd>
              </dl>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card hover>
        <CardContent className="p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center">
                <TrendingUp className="w-5 h-5 text-white" />
              </div>
            </div>
            <div className="ml-5 w-0 flex-1">
              <dl>
                <dt className="text-sm font-medium text-gray-500 truncate">
                  Budget Variance
                </dt>
                <dd className={`text-lg font-bold ${getVarianceColor(data.budgetVariance)}`}>
                  {getVarianceIcon(data.budgetVariance)} {formatCurrency(Math.abs(data.budgetVariance))}
                </dd>
                <dd className="text-xs text-gray-500">
                  {data.budgetVariance < 0 ? 'Under budget' : 'Over budget'}
                </dd>
              </dl>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card hover>
        <CardContent className="p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-purple-500 rounded-lg flex items-center justify-center">
                <Package className="w-5 h-5 text-white" />
              </div>
            </div>
            <div className="ml-5 w-0 flex-1">
              <dl>
                <dt className="text-sm font-medium text-gray-500 truncate">
                  Materials Used
                </dt>
                <dd className="text-lg font-bold text-gray-900">
                  {data.materialsUsed.toLocaleString()}
                </dd>
                <dd className="text-xs text-gray-500">
                  Items logged
                </dd>
              </dl>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card hover>
        <CardContent className="p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-yellow-500 rounded-lg flex items-center justify-center">
                <Image className="w-5 h-5 text-white" />
              </div>
            </div>
            <div className="ml-5 w-0 flex-1">
              <dl>
                <dt className="text-sm font-medium text-gray-500 truncate">
                  Photos
                </dt>
                <dd className="text-lg font-bold text-gray-900">
                  {data.photosUploaded.toLocaleString()}
                </dd>
                <dd className="text-xs text-gray-500">
                  Uploaded
                </dd>
              </dl>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card hover>
        <CardContent className="p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-indigo-500 rounded-lg flex items-center justify-center">
                <Calendar className="w-5 h-5 text-white" />
              </div>
            </div>
            <div className="ml-5 w-0 flex-1">
              <dl>
                <dt className="text-sm font-medium text-gray-500 truncate">
                  Days Active
                </dt>
                <dd className="text-lg font-bold text-gray-900">
                  {data.daysActive}
                </dd>
                <dd className="text-xs text-gray-500">
                  Since start
                </dd>
              </dl>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card hover>
        <CardContent className="p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-red-500 rounded-lg flex items-center justify-center">
                <Target className="w-5 h-5 text-white" />
              </div>
            </div>
            <div className="ml-5 w-0 flex-1">
              <dl>
                <dt className="text-sm font-medium text-gray-500 truncate">
                  Completion
                </dt>
                <dd className="text-lg font-bold text-gray-900">
                  {data.completionPercentage}%
                </dd>
                <dd className="text-xs text-gray-500">
                  Progress
                </dd>
              </dl>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
