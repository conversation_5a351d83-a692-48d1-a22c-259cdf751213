import React from 'react';
import { Image, FolderOpen, HardDrive, Camera } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/Card';

interface PhotoStats {
  totalPhotos: number;
  photosByCategory: Array<{ category: string; count: number }>;
  recentPhotos: any[];
  totalSize: number;
}

interface PhotoStatsCardsProps {
  stats: PhotoStats;
}

export const PhotoStatsCards: React.FC<PhotoStatsCardsProps> = ({ stats }) => {
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const topCategory = stats.photosByCategory.length > 0 
    ? stats.photosByCategory.reduce((prev, current) => 
        prev.count > current.count ? prev : current
      )
    : null;

  const recentPhotosCount = stats.recentPhotos.length;

  return (
    <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                <Image className="w-5 h-5 text-white" />
              </div>
            </div>
            <div className="ml-5 w-0 flex-1">
              <dl>
                <dt className="text-sm font-medium text-gray-500 truncate">
                  Total Photos
                </dt>
                <dd className="text-lg font-medium text-gray-900">
                  {stats.totalPhotos.toLocaleString()}
                </dd>
              </dl>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent className="p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                <FolderOpen className="w-5 h-5 text-white" />
              </div>
            </div>
            <div className="ml-5 w-0 flex-1">
              <dl>
                <dt className="text-sm font-medium text-gray-500 truncate">
                  Categories
                </dt>
                <dd className="text-lg font-medium text-gray-900">
                  {stats.photosByCategory.length}
                </dd>
                {topCategory && (
                  <dd className="text-sm text-gray-500">
                    Top: {topCategory.category}
                  </dd>
                )}
              </dl>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent className="p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                <Camera className="w-5 h-5 text-white" />
              </div>
            </div>
            <div className="ml-5 w-0 flex-1">
              <dl>
                <dt className="text-sm font-medium text-gray-500 truncate">
                  Recent Photos
                </dt>
                <dd className="text-lg font-medium text-gray-900">
                  {recentPhotosCount}
                </dd>
                <dd className="text-sm text-gray-500">
                  Last 7 days
                </dd>
              </dl>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent className="p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
                <HardDrive className="w-5 h-5 text-white" />
              </div>
            </div>
            <div className="ml-5 w-0 flex-1">
              <dl>
                <dt className="text-sm font-medium text-gray-500 truncate">
                  Storage Used
                </dt>
                <dd className="text-lg font-medium text-gray-900">
                  {formatFileSize(stats.totalSize)}
                </dd>
              </dl>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
