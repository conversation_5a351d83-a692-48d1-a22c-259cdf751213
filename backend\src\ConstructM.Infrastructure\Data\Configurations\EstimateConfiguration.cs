using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using ConstructM.Domain.Entities;

namespace ConstructM.Infrastructure.Data.Configurations;

public class EstimateConfiguration : IEntityTypeConfiguration<Estimate>
{
    public void Configure(EntityTypeBuilder<Estimate> builder)
    {
        builder.ToTable("Estimates");

        builder.HasKey(e => e.Id);

        builder.Property(e => e.Name)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(e => e.Description)
            .HasMaxLength(500);

        builder.Property(e => e.Status)
            .IsRequired()
            .HasConversion<int>();

        builder.Property(e => e.TotalCost)
            .HasColumnType("decimal(18,2)");

        builder.Property(e => e.LaborCost)
            .HasColumnType("decimal(18,2)");

        builder.Property(e => e.MaterialCost)
            .HasColumnType("decimal(18,2)");

        builder.Property(e => e.EquipmentCost)
            .HasColumnType("decimal(18,2)");

        builder.Property(e => e.OverheadCost)
            .HasColumnType("decimal(18,2)");

        builder.Property(e => e.ProfitMargin)
            .HasColumnType("decimal(18,2)");

        builder.Property(e => e.ProfitMarginPercentage)
            .HasColumnType("decimal(5,2)");

        builder.Property(e => e.Notes)
            .HasMaxLength(1000);

        builder.Property(e => e.EstimateData)
            .HasColumnType("jsonb"); // PostgreSQL JSON column

        // Relationships
        builder.HasOne(e => e.Site)
            .WithMany(s => s.Estimates)
            .HasForeignKey(e => e.SiteId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(e => e.Items)
            .WithOne(ei => ei.Estimate)
            .HasForeignKey(ei => ei.EstimateId)
            .OnDelete(DeleteBehavior.Cascade);

        // Indexes
        builder.HasIndex(e => e.SiteId);
        builder.HasIndex(e => e.Status);
    }
}

public class EstimateItemConfiguration : IEntityTypeConfiguration<EstimateItem>
{
    public void Configure(EntityTypeBuilder<EstimateItem> builder)
    {
        builder.ToTable("EstimateItems");

        builder.HasKey(ei => ei.Id);

        builder.Property(ei => ei.Name)
            .IsRequired()
            .HasMaxLength(200);

        builder.Property(ei => ei.Description)
            .HasMaxLength(500);

        builder.Property(ei => ei.Category)
            .IsRequired()
            .HasConversion<int>();

        builder.Property(ei => ei.Quantity)
            .HasColumnType("decimal(18,4)");

        builder.Property(ei => ei.Unit)
            .IsRequired()
            .HasMaxLength(20);

        builder.Property(ei => ei.UnitCost)
            .HasColumnType("decimal(18,2)");

        builder.Property(ei => ei.TotalCost)
            .HasColumnType("decimal(18,2)");

        builder.Property(ei => ei.Notes)
            .HasMaxLength(500);

        // Relationships
        builder.HasOne(ei => ei.Estimate)
            .WithMany(e => e.Items)
            .HasForeignKey(ei => ei.EstimateId)
            .OnDelete(DeleteBehavior.Cascade);

        // Indexes
        builder.HasIndex(ei => ei.EstimateId);
        builder.HasIndex(ei => ei.Category);
    }
}
