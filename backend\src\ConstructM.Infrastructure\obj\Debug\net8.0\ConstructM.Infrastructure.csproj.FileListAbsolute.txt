D:\DataOps Sync\Construct-M\backend\src\ConstructM.Infrastructure\bin\Debug\net8.0\ConstructM.Infrastructure.deps.json
D:\DataOps Sync\Construct-M\backend\src\ConstructM.Infrastructure\bin\Debug\net8.0\ConstructM.Infrastructure.runtimeconfig.json
D:\DataOps Sync\Construct-M\backend\src\ConstructM.Infrastructure\bin\Debug\net8.0\ConstructM.Infrastructure.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.Infrastructure\bin\Debug\net8.0\ConstructM.Infrastructure.pdb
D:\DataOps Sync\Construct-M\backend\src\ConstructM.Infrastructure\bin\Debug\net8.0\ConstructM.Domain.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.Infrastructure\bin\Debug\net8.0\ConstructM.Domain.pdb
D:\DataOps Sync\Construct-M\backend\src\ConstructM.Infrastructure\obj\Debug\net8.0\ConstructM.Infrastructure.csproj.AssemblyReference.cache
D:\DataOps Sync\Construct-M\backend\src\ConstructM.Infrastructure\obj\Debug\net8.0\ConstructM.Infrastructure.GeneratedMSBuildEditorConfig.editorconfig
D:\DataOps Sync\Construct-M\backend\src\ConstructM.Infrastructure\obj\Debug\net8.0\ConstructM.Infrastructure.AssemblyInfoInputs.cache
D:\DataOps Sync\Construct-M\backend\src\ConstructM.Infrastructure\obj\Debug\net8.0\ConstructM.Infrastructure.AssemblyInfo.cs
D:\DataOps Sync\Construct-M\backend\src\ConstructM.Infrastructure\obj\Debug\net8.0\ConstructM.Infrastructure.csproj.CoreCompileInputs.cache
D:\DataOps Sync\Construct-M\backend\src\ConstructM.Infrastructure\obj\Debug\net8.0\Construc.1DF66BE1.Up2Date
D:\DataOps Sync\Construct-M\backend\src\ConstructM.Infrastructure\obj\Debug\net8.0\ConstructM.Infrastructure.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.Infrastructure\obj\Debug\net8.0\refint\ConstructM.Infrastructure.dll
D:\DataOps Sync\Construct-M\backend\src\ConstructM.Infrastructure\obj\Debug\net8.0\ConstructM.Infrastructure.pdb
D:\DataOps Sync\Construct-M\backend\src\ConstructM.Infrastructure\obj\Debug\net8.0\ConstructM.Infrastructure.genruntimeconfig.cache
D:\DataOps Sync\Construct-M\backend\src\ConstructM.Infrastructure\obj\Debug\net8.0\ref\ConstructM.Infrastructure.dll
